/** @type {import('tailwindcss').Config} */
// eslint-disable-next-line @typescript-eslint/no-require-imports
const defaultTheme = require('tailwindcss/defaultTheme')
// eslint-disable-next-line @typescript-eslint/no-require-imports
const primeui = require('tailwindcss-primeui')

export default {
  darkMode: 'class',
  content: [
    './public/index.html',
    './src/**/*.{vue,js,ts,jsx,tsx}',
    './node_modules/preline/preline.js',
  ],
  theme: {
    extend: {
      fontFamily: {
        sans: ['Inter var', ...defaultTheme.fontFamily.sans],
      },
      colors: {
        toroblue: {
          50: '#edf2fb',
          100: '#dbe4f7',
          200: '#b7c9ef',
          300: '#93afe7',
          400: '#6f94df',
          500: '#245494',
          600: '#1d4377',
          700: '#17325a',
          800: '#11213c',
          900: '#0a111f',
          950: '#050910',
        },
        history: {
          DEFAULT: '#1c5253',
        },
        primary: {
          DEFAULT: '#245494',
        },
      },
    },
  },
  plugins: [primeui],
}
