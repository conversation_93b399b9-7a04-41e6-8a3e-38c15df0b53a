/* eslint-disable */
/* tslint:disable */
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

export interface PaginatedListOfCodingResponse {
  items?: CodingResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface CodingResponse {
  id?: string
  code?: string
  codeSystem?: string
  codeSystemName?: string
  codeSystemVersion?: string
  displayName?: string
}

export interface PaginatedListOfAppointmentResponse {
  items?: AppointmentResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface AppointmentResponse {
  id?: string
  encounterId?: string
  employeeName?: string
  location?: string
  locationAddress?: string
  /** @format date-time */
  startAt?: string
  /** @format int32 */
  durationInMinutes?: number
  /** AppointmentStatus */
  status?: AppointmentStatus
  /** @format date-time */
  checkInAvailableAt?: string
  /** @format int32 */
  checkInStartOffsetHours?: number
  /** @format decimal */
  missedAppointmentFeeInCents?: number
  timeZone?: string
  currentTimeFilter?: AppointmentTimeFilter | null
}

/** AppointmentStatus */
export enum AppointmentStatus {
  Canceled = 'Canceled',
  CanceledLate = 'Canceled Late',
  CheckedIn = 'Checked In',
  Completed = 'Completed',
  Confirmed = 'Confirmed',
  Missed = 'Missed',
  Pending = 'Pending',
}

/** A base type to use for creating smart enums. */
export interface SmartEnumOfAppointmentStatusAndString {
  /** Gets the name. */
  name?: string | null
  /** Gets the value. */
  value?: string | null
}

/** AppointmentTimeFilter */
export enum AppointmentTimeFilter {
  All = 'All',
  Past = 'Past',
  Upcoming = 'Upcoming',
}

/** A base type to use for creating smart enums. */
export interface SmartEnumOfAppointmentTimeFilterAndString {
  /** Gets the name. */
  name?: string | null
  /** Gets the value. */
  value?: string | null
}

export interface CalendarAppointmentResponse {
  id?: string
  title?: string
  /** @format date-time */
  start?: string
  /** @format date-time */
  end?: string
  color?: string
  extendedProps?: CalendarAppointmentDetailsResponse
}

export interface CalendarAppointmentDetailsResponse {
  patientFullName?: string
  employeeShortName?: string
  locationName?: string
  color?: string
}

export interface AppointmentDetailsResponse {
  id?: string
  employeeId?: string
  locationId?: string
  /** AppointmentStatus */
  status?: AppointmentStatus
  patientFullName?: string
  /** @format date-time */
  startAt?: string
  /** @format int32 */
  durationInMinutes?: number
}

export type CreateNewPatientAppointmentCommand = AuthRequestOfString & {
  firstName?: string
  lastName?: string
  /** @format date-time */
  birthday?: string
  email?: string
  phoneNumber?: string
  employeeId?: string
  locationId?: string
  /** @format int32 */
  durationInMinutes?: number
  /** @format date-time */
  startAt?: string
}

export interface AuthRequestOfString {
  /** @format date-time */
  timestamp?: string
}

export type CreateExistingPatientAppointmentCommand = AuthRequestOfString & {
  patientId?: string
  employeeId?: string
  locationId?: string
  /** @format int32 */
  durationInMinutes?: number
  /** @format date-time */
  startAt?: string
  initiatedByPatient?: boolean
}

export type EditAppointmentCommand = AuthRequestOfString & {
  id?: string
  employeeId?: string
  locationId?: string
  /** @format date-time */
  startAt?: string
  /** @format int32 */
  durationInMinutes?: number
}

export type CancelAppointmentCommand = AuthRequestOfString & {
  id?: string
}

export type MarkAppointmentAsCheckedInCommand = AuthRequestOfString & {
  id?: string
}

export type ConfirmAppointmentCommand = AuthRequestOfString & {
  id?: string
}

export interface ExistingAppointmentResponse {
  appointmentId?: string
  /** @format date-time */
  startAt?: string
  /** @format date-time */
  endAt?: string
  employeeName?: string
  locationName?: string
  /** AppointmentStatus */
  status?: AppointmentStatus
}

export type LoginUserCommand = AnonRequestOfString & {
  email?: string
  password?: string
}

export interface AnonRequestOfString {
  /** @format date-time */
  timestamp?: string
}

export interface UserInfoResponse {
  userId?: string
  email?: string
  firstName?: string
  lastName?: string
  selectedUserRole?: string
  employeeId?: string | null
  patientId?: string | null
  organizationId?: string | null
  organizationName?: string | null
  locationId?: string | null
  locationName?: string | null
  timeZone?: string | null
  locationEmployeeRoles?: string[] | null
  availableLocations?: AvailableLocationResponse[]
  availableUserRoles?: string[]
}

export interface AvailableLocationResponse {
  organizationId?: string
  organizationName?: string | null
  locationId?: string
  locationName?: string | null
}

export type SetPasswordEmployeeCommand = AnonRequestOfString & {
  invitationToken?: string
  password?: string
}

export type SetPasswordPatientCommand = AnonRequestOfString & {
  invitationToken?: string
  password?: string
}

/** Represents a void type, since Void is not a valid return type in C#. */
export type Unit = object

export type UpdateSessionCommand = AnonRequestOfUnit & {
  selectedUserRole?: string
  selectedOrganizationId?: string | null
  selectedLocationId?: string | null
}

export interface AnonRequestOfUnit {
  /** @format date-time */
  timestamp?: string
}

export interface PaginatedListOfCptCodeResponse {
  items?: CptCodeResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface CptCodeResponse {
  id?: string
  code?: string
  conceptId?: string
  clinicianDescriptor?: string
}

export interface PaginatedListOfEmployeeResponse {
  items?: EmployeeResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface EmployeeResponse {
  id?: string
  email?: string
  firstName?: string
  lastName?: string
  roles?: string[]
  timeZone?: string
  npi?: string
  phoneNumber?: string
  address?: AddressResponse | null
  /** @format date-time */
  lastSeen?: string | null
}

export interface AddressResponse {
  street?: string
  city?: string
  state?: string
  zipCode?: string
}

export interface SelectListItem {
  text?: string | null
  value?: string | null
}

export type CreateEmployeeCommand = AuthRequestOfString & {
  organizationId?: string
  locationId?: string
  roles?: string[]
  email?: string
  firstName?: string
  lastName?: string
  npi?: string
  phoneNumber?: string
  timeZone?: string
  calendarColor?: string
  address?: AddressRequest
}

export interface AddressRequest {
  street?: string
  city?: string
  state?: string
  zipCode?: string
}

export type EditEmployeeCommand = AuthRequestOfString & {
  id?: string
  organizationId?: string
  roles?: string[]
  email?: string
  firstName?: string
  lastName?: string
  npi?: string
  phoneNumber?: string
  timeZone?: string
  calendarColor?: string
  address?: AddressRequest
}

export interface LocationEmployeeResponse {
  calendarColor?: string
  /** @format int32 */
  numberOfAppointmentOverlaps?: number
  /** @format int32 */
  appointmentDurationInMinutes?: number
  pendingApprovalAppointments?: boolean
  specialtyClassification?: string | null
  receivedNotificationPreferences?: string[]
  officeHours?: OfficeHours[]
  outOfOfficeHours?: OutOfOfficeHours[]
}

export interface OfficeHours {
  day?: DayOfWeek
  /** @format duration */
  openTime?: string | null
  /** @format duration */
  closeTime?: string | null
  exclusions?: Exclusion[]
}

export enum DayOfWeek {
  Sunday = 0,
  Monday = 1,
  Tuesday = 2,
  Wednesday = 3,
  Thursday = 4,
  Friday = 5,
  Saturday = 6,
}

export interface Exclusion {
  title?: string | null
  /** @format duration */
  from?: string
  /** @format duration */
  to?: string
}

export interface OutOfOfficeHours {
  /** @format date-time */
  startAt?: string
  /** @format date-time */
  endAt?: string
}

export interface EditGeneralSettingsRequest {
  calendarColor?: string
  /** @format int32 */
  numberOfAppointmentOverlaps?: number
  pendingApprovalAppointments?: boolean
  /** @format int32 */
  appointmentDurationInMinutes?: number
  receivedNotificationPreferences?: string[]
  specialtyClassification?: string | null
}

export interface EditOfficeHoursRequest {
  officeHours?: OfficeHours[]
}

export interface EditOutOfOfficeHoursRequest {
  outOfOfficeHoursList?: OutOfOfficeHours[]
}

export interface AvailableAppointmentSlotsResponse {
  availableSlots?: AppointmentSlot[]
  existingAppointment?: ExistingAppointmentInfo | null
}

export interface AppointmentSlot {
  /** @format date-time */
  from?: string
  /** @format date-time */
  to?: string
  /** @format int32 */
  durationInMinutes?: number
  practitionerTimeZone?: string
}

export interface ExistingAppointmentInfo {
  appointmentId?: string
  /** @format date-time */
  startAt?: string
  /** @format date-time */
  endAt?: string
  employeeName?: string
  locationName?: string
  status?: string
}

export interface EncounterDetailsResponse {
  id?: string
  /** @format date-time */
  encounterDate?: string
  encounterLocationName?: string
  patientId?: string
  patientMrn?: string
  patientName?: string
  /** @format date-time */
  patientDateOfBirth?: string
  patientAddress?: string | null
  patientPhoneNumber?: string
  patientSex?: string
  practitionerName?: string
  notes?: PatientNoteResponse[]
  vitalSigns?: VitalSignResponse[]
  questionnaires?: EncounterQuestionnaireResponse[]
  medications?: EncounterMedicationResponse[]
}

export interface PatientNoteResponse {
  id?: string
  noteId?: string
  /** @format int32 */
  version?: number
  name?: string
  classification?: string
  practitionerName?: string
  /** @format date-time */
  createdAt?: string
  fields?: NoteFieldResponse[]
  cptCode?: string
  icdCodes?: string[]
}

export interface NoteFieldResponse {
  name?: string
  value?: string
  isRequired?: boolean
}

export interface VitalSignResponse {
  id?: string
  /** @format date-time */
  recordedDate?: string | null
  recordedBy?: string | null
  type?: string
  value?: string
  status?: string
  /** @format date-time */
  createdAt?: string
  /** @format date-time */
  modifiedAt?: string | null
  createdBy?: string
  modifiedBy?: string | null
}

export interface EncounterQuestionnaireResponse {
  title?: string
  locationId?: string | null
  questions?: QuestionResponse[]
}

export interface QuestionResponse {
  text?: string
  answers?: string[]
}

export interface EncounterMedicationResponse {
  name?: string
  frequency?: string
  duration?: string
  instructions?: string
}

export interface EncounterResponse {
  id?: string
  scratchText?: string | null
  patientId?: string
  email?: string
  fullName?: string
  /** @format date-time */
  patientBirthday?: string
  phoneNumber?: string | null
  preferredContactMethod?: string | null
  /** @format date-time */
  previouesEncounterDate?: string | null
}

export interface PaginatedListOfActiveEncounterResponse {
  items?: ActiveEncounterResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface ActiveEncounterResponse {
  id?: string
  patientId?: string
  patientName?: string
  practitionerId?: string
  practitionerName?: string
  /** @format date-time */
  startAt?: string
  /** EncounterStatus */
  status?: EncounterStatus
  hasNotes?: boolean
}

/** EncounterStatus */
export enum EncounterStatus {
  Arrived = 'Arrived',
  Canceled = 'Canceled',
  CanceledLate = 'Canceled Late',
  CheckedIn = 'Checked In',
  Completed = 'Completed',
  InProgress = 'In Progress',
  Missed = 'Missed',
  Planned = 'Planned',
}

/** A base type to use for creating smart enums. */
export interface SmartEnumOfEncounterStatusAndString {
  /** Gets the name. */
  name?: string | null
  /** Gets the value. */
  value?: string | null
}

export type UpdateScratchTextCommand = AuthRequestOfUnit & {
  patientId?: string
  scratchText?: string
}

export interface AuthRequestOfUnit {
  /** @format date-time */
  timestamp?: string
}

export type AddVitalSignsCommand = AuthRequestOfUnit & {
  patientId?: string
  encounterId?: string
  status?: string
  vitals?: VitalSignRequest[]
}

export interface VitalSignRequest {
  id?: string | null
  value?: string
  type?: string
}

export type CreatePatientProblemCommand = AuthRequestOfString & {
  patientId?: string
  encounterId?: string
  condition?: string
  clinicalConditionStatus?: string
  conditionVerificationStatus?: string
  /** @format date-time */
  diagnosisDate?: string
  /** @format date-time */
  abatement?: string | null
}

export type UpdateEncounterStatusCommand = AuthRequestOfString & {
  encounterId?: string
  status?: string
}

export type CheckoutEncounterCommand = AuthRequestOfUnit & {
  encounterId?: string
}

export interface EncounterCommunicationMessageResponse {
  id?: string
  patientId?: string
  subject?: string
  message?: string
  attachments?: (string | null)[]
  type?: string
  /** @format date-time */
  sentAt?: string
  senderName?: string
  recipientName?: string
  sendByPatient?: boolean
  seenByRecipient?: boolean
}

export interface PaginatedListOfSearchOrderEntryResponse {
  items?: SearchOrderEntryResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface SearchOrderEntryResponse {
  id?: string
  code?: string
  displayName?: string
  type?: string
}

export type OrderResponse = BaseOrderResponse &
  (
    | BaseOrderResponseOrderTypeMapping<'Med', MedicationOrderResponse>
    | BaseOrderResponseOrderTypeMapping<'Lab', LabOrderResponse>
    | BaseOrderResponseOrderTypeMapping<'Procedure', ProcedureOrderResponse>
    | BaseOrderResponseOrderTypeMapping<'Bundle', BundleOrderResponse>
  )

export type MedicationOrderResponse = BaseOrderResponse & {
  medicationId?: string
  ingredientId?: string
  frequency?: string
  customFrequency?: string | null
  duration?: string
  prn?: boolean
  prnReason?: string | null
  /** @format date-time */
  startTime?: string
  instructions?: string
  timingStatus?: string | null
}

export type LabOrderResponse = BaseOrderResponse & {
  loincCodeId?: string
  specimen?: string
  note?: string | null
  fasting?: boolean
  repeat?: boolean
}

export type ProcedureOrderResponse = BaseOrderResponse & {
  snomedCodeId?: string
  note?: string | null
  fasting?: boolean
  repeat?: boolean
}

export type BundleOrderResponse = BaseOrderResponse & {
  bundleTemplateId?: string
  orders?: OrderResponse[]
}

export type CreateOrderMedicationCommand = AuthRequestOfString & {
  encounterId?: string
  patientId?: string
  medicationId?: string
  ingredientId?: string
  frequency?: string
  customFrequency?: string
  duration?: string
  prn?: boolean
  prnReason?: string | null
  /** @format date-time */
  startTime?: string
  instructions?: string
}

export type EditOrderMedicationCommand = AuthRequestOfUnit & {
  id?: string
  bundleId?: string | null
  medicationId?: string
  ingredientId?: string
  frequency?: string
  customFrequency?: string | null
  duration?: string
  prn?: boolean
  prnReason?: string | null
  /** @format date-time */
  startTime?: string
  instructions?: string
}

export type CreateOrderLabCommand = AuthRequestOfString & {
  encounterId?: string
  patientId?: string
  loincCodeId?: string
  specimen?: string
  note?: string
  fasting?: boolean
  repeat?: boolean
  priority?: string
}

export type EditOrderLabCommand = AuthRequestOfUnit & {
  id?: string
  bundleId?: string | null
  loincCodeId?: string
  specimen?: string
  note?: string
  fasting?: boolean
  repeat?: boolean
  priority?: string
}

export type CreateOrderProcedureCommand = AuthRequestOfString & {
  encounterId?: string
  patientId?: string
  snomedCodeId?: string
  note?: string
  fasting?: boolean
  repeat?: boolean
  priority?: string
}

export type EditOrderProcedureCommand = AuthRequestOfUnit & {
  id?: string
  bundleId?: string | null
  snomedCodeId?: string
  note?: string
  fasting?: boolean
  repeat?: boolean
  priority?: string
}

export type CreateOrderBundleCommand = AuthRequestOfString & {
  encounterId?: string
  patientId?: string
  bundleTemplateId?: string
}

export type EditOrderBundleCommand = AuthRequestOfUnit & {
  id?: string
  /** @format date-time */
  startTime?: string | null
  note?: string | null
  fasting?: boolean | null
  repeat?: boolean | null
  priority?: string | null
}

export type ChangeOrderStatusCommand = AuthRequestOfUnit & {
  orderIds?: string[]
  status?: string
  bundleId?: string | null
}

export type SaveOrdersAsBundleCommand = AuthRequestOfUnit & {
  orderIds?: string[]
  encounterId?: string
  name?: string
  priority?: string
}

export type DeleteOrderCommand = AuthRequestOfUnit & {
  id?: string
  bundleId?: string | null
}

export interface MedicationRecordResponse {
  id?: string
  medication?: string
  dose?: string | null
  route?: string | null
  medicationId?: string
  frequency?: string
  customFrequency?: string
  duration?: string
  prn?: boolean
  prnReason?: string | null
  /** @format date-time */
  startTime?: string
  instructions?: string
  priority?: string
  status?: string
  timingStatus?: string | null
  requester?: string | null
  note?: string | null
  administations?: Administation[]
}

export interface Administation {
  /** @format date-time */
  date?: string
  administredById?: string
  administredByName?: string
}

export type ChangeOrderTimingStatusCommand = AuthRequestOfUnit & {
  orderIds?: string[]
  status?: string
}

export type ChangeOrderNoteCommand = AuthRequestOfUnit & {
  orderId?: string
  bundleId?: string | null
  note?: string | null
}

export interface ProcessPaymentResponse {
  success?: boolean
  message?: string
}

export type ProcessPaymentIposCommand = AuthRequestOfProcessPaymentResponse & {
  encounterId?: string
  /** @format double */
  amount?: number
}

export interface AuthRequestOfProcessPaymentResponse {
  /** @format date-time */
  timestamp?: string
}

export type ProcessPaymentIposCloudCommand = AuthRequestOfProcessPaymentResponse & {
  encounterId?: string
  /** @format decimal */
  amount?: number
  cardId?: string
}

export interface PaginatedListOfEncountersBillingResponse {
  items?: EncountersBillingResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface EncountersBillingResponse {
  id?: string
  patientName?: string
  /** @format date-time */
  encounterDate?: string
  phoneNumber?: string | null
  /** @format decimal */
  amountCharged?: number
  /** @format decimal */
  amountRefunded?: number
  paymentStatus?: string
  /** EncounterStatus */
  businessStatus?: EncounterStatus
}

export interface EncounterTransactionResponse {
  id?: string
  /** @format date-time */
  date?: string
  /** @format decimal */
  amount?: number
  transactionType?: string
  paymentMethod?: string
  cardType?: string
  last4?: string
  status?: string
}

export type CustomRefundCommand = AuthRequestOfProcessPaymentResponse & {
  transactionId?: string
  /** @format decimal */
  refundAmount?: number
}

export interface PaginatedListOfLocationResponse {
  items?: LocationResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface LocationResponse {
  id?: string
  name?: string
  classification?: string | null
  isDefault?: boolean
  phoneNumber?: string
  taxIdentificationNumber?: string
  /** @format int32 */
  markMissedTime?: number
  /** @format decimal */
  missedFeeInCents?: number
  /** @format int32 */
  checkInStartOffsetHours?: number
  iposTpn?: string | null
  iposAuthKey?: string | null
  iposCloudTpn?: string | null
  iposCloudAuthKey?: string | null
  address?: AddressResponse | null
}

export type CreateLocationCommand = AuthRequestOfString & {
  name?: string
  classification?: string
  isDefault?: boolean
  phoneNumber?: string
  taxIdentificationNumber?: string
  /** @format int32 */
  markMissedTime?: number
  /** @format decimal */
  missedFeeInCents?: number
  /** @format int32 */
  checkInStartOffsetHours?: number
  iposTpn?: string
  iposAuthKey?: string
  iposCloudTpn?: string
  iposCloudAuthKey?: string
  address?: AddressRequest
}

export type EditLocationCommand = AuthRequestOfString & {
  id?: string
  name?: string
  classification?: string
  isDefault?: boolean
  phoneNumber?: string
  taxIdentificationNumber?: string
  /** @format int32 */
  markMissedTime?: number
  /** @format decimal */
  missedFeeInCents?: number
  /** @format int32 */
  checkInStartOffsetHours?: number
  iposTpn?: string
  iposAuthKey?: string
  iposCloudTpn?: string
  iposCloudAuthKey?: string
  address?: AddressRequest
}

export type DeactivateLocationCommand = AuthRequestOfUnit & {
  id?: string
}

export interface LocationWithPractitionersResponse {
  locationId?: string
  locationName?: string
  organizationName?: string
  practitioners?: PractitionerResponse[]
}

export interface PractitionerResponse {
  employeeId?: string
  fullName?: string
}

export interface LoincCodeDetailsResponse {
  id?: string | null
  loincNum?: string | null
  component?: string | null
  property?: string | null
  timeAspct?: string | null
  system?: string | null
  scaleTyp?: string | null
  methodTyp?: string | null
  class?: string | null
  versionLastChanged?: string | null
  chngType?: string | null
  definitionDescription?: string | null
  status?: string | null
  consumerName?: string | null
  classtype?: string | null
  formula?: string | null
  exmplAnswers?: string | null
  surveyQuestText?: string | null
  surveyQuestSrc?: string | null
  unitsRequired?: boolean
  relatedNames2?: string | null
  shortName?: string | null
  orderObs?: string | null
  hl7FieldSubfieldId?: string | null
  externalCopyrightNotice?: string | null
  exampleUnits?: string | null
  longCommonName?: string | null
  exampleUcumUnits?: string | null
  statusReason?: string | null
  statusText?: string | null
  changeReasonPublic?: string | null
  /** @format int32 */
  commonTestRank?: number | null
  /** @format int32 */
  commonOrderRank?: number | null
  hl7AttachmentStructure?: string | null
  externalCopyrightLink?: string | null
  panelType?: string | null
  askAtOrderEntry?: string | null
  associatedObservations?: string | null
  versionFirstReleased?: string | null
  validHl7AttachmentRequest?: boolean
  displayName?: string | null
}

export type CreateNoteCommand = AuthRequestOfUnit & {
  name?: string
  classification?: string
  patientId?: string
  encounterId?: string
  fields?: NoteFieldRequest[]
  cptCode?: string
  icdCodes?: string[]
}

export interface NoteFieldRequest {
  name?: string
  value?: string
}

export type AmendNoteCommand = AuthRequestOfUnit & {
  id?: string
  patientId?: string
  encounterId?: string
  fields?: NoteFieldRequest[]
  icdCodes?: string[]
  cptCode?: string
}

export interface PaginatedListOfNoteTemplateResponse {
  items?: NoteTemplateResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface NoteTemplateResponse {
  id?: string
  name?: string
  classification?: string
  specialization?: string | null
  documentType?: string
  organization?: string
  locations?: string
  creator?: string
}

export interface NoteTemplateDetailsResponse {
  id?: string
  name?: string
  classification?: string
  specialization?: string | null
  documentType?: string
  fields?: NoteTemplateFieldResponse[]
  organization?: NamedEntityResponse
  locations?: NamedEntityResponse[]
}

export interface NoteTemplateFieldResponse {
  name?: string
  value?: string
  isRequired?: boolean
}

export interface NamedEntityResponse {
  id?: string
  name?: string
}

export type CreateNoteTemplateCommand = AuthRequestOfString & {
  name?: string
  classification?: string
  specialization?: string | null
  specialityCode?: string
  documentType?: string
  fields?: NoteTemplateFieldRequest[]
  locationIds?: string[]
}

export interface NoteTemplateFieldRequest {
  name?: string
  value?: string
  isRequired?: boolean
}

export type UpdateNoteTemplateCommand = AuthRequestOfString & {
  id?: string
  name?: string
  classification?: string
  specialization?: string | null
  specialityCode?: string
  documentType?: string
  fields?: NoteTemplateFieldRequest[]
  locationIds?: string[]
}

export interface PaginatedListOfNotificationResponse {
  items?: NotificationResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface NotificationResponse {
  id?: string
  /** NotificationType */
  notificationType?: NotificationType
  /** NotificationStatus */
  status?: NotificationStatus
  title?: string
  message?: string
  relatedEntityId?: string | null
  relatedEntityType?: string | null
  /** @format date-time */
  createdAt?: string
  /** @format date-time */
  readAt?: string | null
  actionCompleted?: boolean
}

/** NotificationType */
export enum NotificationType {
  AppointmentCanceled = 'Appointment Canceled',
  AppointmentConfirmed = 'Appointment Confirmed',
  AppointmentMissed = 'Appointment Missed',
  AppointmentRequested = 'Appointment Requested',
  AppointmentRescheduled = 'Appointment Rescheduled',
  CompleteProfile = 'Complete Profile',
  CompleteQuestionnaires = 'Complete Questionnaires',
  QuestionnaireUpdated = 'Questionnaire Updated',
}

/** A base type to use for creating smart enums. */
export interface SmartEnumOfNotificationTypeAndString {
  /** Gets the name. */
  name?: string | null
  /** Gets the value. */
  value?: string | null
}

/** NotificationStatus */
export enum NotificationStatus {
  Read = 'Read',
  Unread = 'Unread',
}

/** A base type to use for creating smart enums. */
export interface SmartEnumOfNotificationStatusAndString {
  /** Gets the name. */
  name?: string | null
  /** Gets the value. */
  value?: string | null
}

export interface PaginatedListOfOrderBundleTemplateListResponse {
  items?: OrderBundleTemplateListResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface OrderBundleTemplateListResponse {
  id?: string
  locationId?: string
  templateName?: string
  priority?: string
  /** @format date-time */
  createdAt?: string
  createdBy?: string
}

export interface OrderBundleTemplateResponse {
  locationId?: string
  templateName?: string
  priority?: string
  /** @format date-time */
  createdAt?: string
  createdBy?: string
  steps?: OrderBundleTemplateStepResponse[]
}

export interface OrderBundleTemplateStepResponse {
  /** @format int32 */
  stepNumber?: number
  orderEntries?: OrderTemplateEntryResponse[]
}

export type OrderTemplateEntryResponse = BaseOrderTemplateEntryResponse &
  (
    | BaseOrderTemplateEntryResponseOrderTypeMapping<'Med', MedicationTemplateEntryResponse>
    | BaseOrderTemplateEntryResponseOrderTypeMapping<'Lab', LabTemplateEntryResponse>
    | BaseOrderTemplateEntryResponseOrderTypeMapping<'Procedure', ProcedureTemplateEntryResponse>
    | BaseOrderTemplateEntryResponseOrderTypeMapping<'Bundle', BundleTemplateEntryResponse>
  )

export type MedicationTemplateEntryResponse = BaseOrderTemplateEntryResponse & {
  medicationId?: string
  ingredientId?: string
  frequency?: string
  customFrequency?: string | null
  duration?: string
  prn?: boolean
  prnReason?: string | null
}

export type LabTemplateEntryResponse = BaseOrderTemplateEntryResponse & {
  loincCodeId?: string
  specimen?: string
  note?: string
  fasting?: boolean
  repeat?: boolean
}

export type ProcedureTemplateEntryResponse = BaseOrderTemplateEntryResponse & {
  loincCodeId?: string
  note?: string
  fasting?: boolean
  repeat?: boolean
}

export type BundleTemplateEntryResponse = BaseOrderTemplateEntryResponse & {
  bundleId?: string
  nestedOrderEntries?: OrderTemplateEntryResponse[]
}

export type CreateOrderBundleTemplateCommand = AuthRequestOfString & {
  locationId?: string
  templateName?: string
  priority?: string
}

export type EditOrderBundleTemplateCommand = AuthRequestOfString & {
  orderBundleTemplateId?: string
  locationId?: string
  templateName?: string
  priority?: string
}

export type AddLabOrderEntryToBundleTemplateCommand = AuthRequestOfString & {
  orderBundleTemplateId?: string
  /** @format int32 */
  stepNumber?: number
  loincCodeId?: string
  specimen?: string
  note?: string
  fasting?: boolean
  repeat?: boolean
  isRequired?: boolean
}

export type EditLabOrderEntryInBundleTemplateCommand = AuthRequestOfString & {
  orderBundleTemplateId?: string
  /** @format int32 */
  stepNumber?: number
  orderEntryId?: string
  nestedBundleId?: string | null
  loincCodeId?: string
  specimen?: string
  note?: string
  fasting?: boolean
  repeat?: boolean
  isRequired?: boolean
}

export type AddProcedureOrderEntryToBundleTemplateCommand = AuthRequestOfString & {
  orderBundleTemplateId?: string
  /** @format int32 */
  stepNumber?: number
  snomedCodeId?: string
  note?: string
  fasting?: boolean
  repeat?: boolean
  isRequired?: boolean
}

export type EditProcedureOrderEntryInBundleTemplateCommand = AuthRequestOfString & {
  orderBundleTemplateId?: string
  /** @format int32 */
  stepNumber?: number
  orderEntryId?: string
  nestedBundleId?: string | null
  snomedCodeId?: string
  note?: string
  fasting?: boolean
  repeat?: boolean
  isRequired?: boolean
}

export type AddMedicationOrderEntryToBundleTemplateCommand = AuthRequestOfString & {
  orderBundleTemplateId?: string
  /** @format int32 */
  stepNumber?: number
  medicationId?: string
  ingredientId?: string
  frequency?: string
  customFrequency?: string | null
  duration?: string
  prn?: boolean
  prnReason?: string | null
  isRequired?: boolean
}

export type EditMedicationOrderEntryInBundleTemplateCommand = AuthRequestOfString & {
  orderBundleTemplateId?: string
  /** @format int32 */
  stepNumber?: number
  orderEntryId?: string
  nestedBundleId?: string | null
  medicationId?: string
  ingredientId?: string
  frequency?: string
  customFrequency?: string | null
  duration?: string
  prn?: boolean
  prnReason?: string | null
  isRequired?: boolean
}

export type AddBundleOrderEntryToBundleTemplateCommand = AuthRequestOfString & {
  orderBundleTemplateId?: string
  /** @format int32 */
  stepNumber?: number
  bundleId?: string
}

export type DeleteOrderEntryFromBundleTemplateCommand = AuthRequestOfUnit & {
  orderBundleTemplateId?: string
  /** @format int32 */
  stepNumber?: number
  orderEntryId?: string
  nestedBundleId?: string | null
}

export type AddOrderBundleTemplateStepCommand = AuthRequestOfString & {
  orderBundleTemplateId?: string
  /** @format int32 */
  stepNumber?: number
}

export type DeleteOrderBundleTemplateStepCommand = AuthRequestOfUnit & {
  orderBundleTemplateId?: string
  /** @format int32 */
  stepNumber?: number
}

export interface PaginatedListOfOrganizationResponse {
  items?: OrganizationResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface OrganizationResponse {
  id?: string
  name?: string
  contactPerson?: ContactPersonResponse
}

export interface ContactPersonResponse {
  fullName?: string
  email?: string
}

export type CreateOrganizationCommand = AuthRequestOfString & {
  organizationName?: string
  locationName?: string
  organizationAdmin?: OrganizationAdminRequest
}

export interface OrganizationAdminRequest {
  firstName?: string
  lastName?: string
  email?: string
}

export type EditOrganizationCommand = AuthRequestOfString & {
  id?: string
  name?: string
}

export interface PaginatedListOfPatientResponse {
  items?: PatientResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface PatientResponse {
  id?: string
  mrn?: string
  email?: string
  firstName?: string
  lastName?: string
  /** @format date-time */
  birthday?: string
  phoneNumber?: string
  preferredContactMethod?: string | null
}

export interface PatientProfileResponse {
  mrn?: string
  firstName?: string
  lastName?: string
  middleName?: string | null
  suffix?: string | null
  preferredName?: string | null
  previousFirstName?: string | null
  previousLastName?: string | null
  preferredLanguage?: string | null
  /** @format date-time */
  birthday?: string
  birthSex?: string | null
  genderIdentity?: string | null
  sexualOrientation?: string | null
  race?: string | null
  ethnicity?: string | null
  tribalAffiliation?: string | null
  documents?: DocumentResponse[]
  address?: AddressResponse | null
  previousAddress?: AddressResponse | null
  socialSecurityNumber?: string | null
  preferredContactMethod?: string | null
  preferredContactName?: string | null
  emails?: EmailAddressResponse[]
  phones?: PhoneNumberResponse[]
  emergencyContacts?: EmergencyContactResponse[]
  medications?: PatientMedicationResponse[]
  allergies?: PatientAllergyResponse[]
}

export interface DocumentResponse {
  docId?: string
  documentType?: string
  /** @format date-time */
  createdAt?: string
  filePaths?: string[]
}

export interface EmailAddressResponse {
  email?: string
  primary?: boolean
}

export interface PhoneNumberResponse {
  number?: string
  type?: string
  primary?: boolean
}

export interface EmergencyContactResponse {
  name?: string
  relationship?: string
  phoneNumber?: string
  primary?: boolean
}

export interface PatientMedicationResponse {
  code?: string
  displayName?: string
}

export interface PatientAllergyResponse {
  code?: string
  displayName?: string
  reaction?: string
  severity?: string
}

export interface PatientDocument {
  documentFiles?: File[]
  documentType?: string
}

export type SetPatientContactInfoCommand = AuthRequestOfUnit & {
  address?: AddressRequest
  previousAddress?: AddressRequest | null
  phoneNumbers?: PhoneNumberRequest[]
  emails?: EmailAddressRequest[]
  preferredContactName?: string
  preferredContactMethod?: string
  socialSecurityNumber?: string
  emergencyContacts?: EmergencyContactRequest[]
}

export interface PhoneNumberRequest {
  number?: string
  type?: string
  isPrimary?: boolean
}

export interface EmailAddressRequest {
  email?: string
  isPrimary?: boolean
}

export interface EmergencyContactRequest {
  name?: string
  relationship?: string
  phoneNumber?: string
  primary?: boolean
}

export interface PatientMedicationRequest {
  code?: string
  displayName?: string
}

export interface PatientAllergyRequest {
  code?: string
  displayName?: string
  reaction?: string
  severity?: string
}

export interface PatientImmunizationResponse {
  id?: string
  displayName?: string
  /** @format date */
  date?: string
}

export type AddPatientImmunizationCommand = AuthRequestOfUnit & {
  code?: string
  displayName?: string
  /** @format date */
  date?: string
}

export type DeletePatientImmunizationCommand = AuthRequestOfUnit & {
  id?: string
}

export interface PatientInsuranceResponse {
  id?: string
  issuer?: string
  groupId?: string
  memberId?: string
  /** @format date */
  start?: string
  /** @format date */
  end?: string | null
  order?: string
  type?: string
  /** @format decimal */
  copay?: number | null
  /** @format decimal */
  deductible?: number | null
  relationship?: string
  subscriber?: SubscriberResponse | null
  cardFrontUrl?: string | null
  cardBackUrl?: string | null
}

export interface SubscriberResponse {
  firstName?: string
  lastName?: string
  /** @format date */
  birthday?: string
  birthSex?: string
}

export interface PatientInsuranceInfoResponse {
  hasInsurance?: boolean
  /** @format decimal */
  primaryCopay?: number | null
}

export type DeletePatientInsuranceCommand = AuthRequestOfUnit & {
  patientInsuranceId?: string
}

export interface PaginatedListOfPatientRecordResponse {
  items?: PatientRecordResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface PatientRecordResponse {
  id?: string
  practitionerName?: string
  locationName?: string
  /** @format date-time */
  startAt?: string
  /** @format date-time */
  startedAt?: string | null
  status?: string
  scratchText?: string | null
  vitalSigns?: VitalSignResponse[]
  hasUnseenMessages?: boolean
}

export interface LaboratoryResultResponse {
  status?: string
  codeSystem?: string | null
  code?: string | null
  codeDisplay?: string | null
  patientId?: string
  encounterId?: string | null
  /** @format date-time */
  effectiveDateTime?: string | null
  /** @format date-time */
  issued?: string | null
  performerId?: string | null
  valueQuantity?: string | null
  valueUnit?: string | null
  interpretationCode?: string | null
  interpretationDisplay?: string | null
  referenceRangeLow?: string | null
  referenceRangeHigh?: string | null
  referenceRangeUnit?: string | null
  comments?: string | null
  seen?: boolean
}

export type MarkLaboratoryResultAsSeenCommand = AuthRequestOfUnit & {
  patientId?: string
  /** @format date-time */
  date?: string
}

export interface ImagingResultResponse {
  id?: string
  status?: string
  patientId?: string
  encounterId?: string | null
  /** @format date-time */
  effectiveDate?: string | null
  /** @format date-time */
  issued?: string | null
  performerId?: string | null
  reference?: string
  files?: ImagingFileResponse[]
}

export interface ImagingFileResponse {
  contentType?: string
  title?: string | null
  url?: string | null
  seen?: boolean
}

export type MarkImagingResultAsSeenCommand = AuthRequestOfUnit & {
  patientId?: string
  id?: string
  url?: string
}

export interface PaymentCardResponse {
  id?: string
  type?: string
  lastFour?: string
  expirationDate?: string
}

export type DeletePatientPaymentCardCommand = AuthRequestOfUnit & {
  cardId?: string
}

export interface PaginatedListOfPatientCommunicationMessageResponse {
  items?: PatientCommunicationMessageResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface PatientCommunicationMessageResponse {
  id?: string
  encounterId?: string | null
  subject?: string
  message?: string
  sentByPatient?: boolean
  practitioner?: string
  seen?: boolean
  attachments?: (string | null)[]
  type?: string
  /** @format date-time */
  sentAt?: string
}

export interface PatientActiveMedication {
  name?: string
  frequency?: string
  duration?: string
  instructions?: string
  practitioner?: string
}

export interface PatientProblemResponse {
  id?: string
  patientId?: string
  conditionDescription?: string
  clinicalStatus?: string
  verificationStatus?: string
  /** @format date */
  diagnosisDate?: string
  /** @format date */
  abatement?: string | null
}

export interface PatientCareResponse {
  careTeamItems?: CareTeamResponse[]
  carePlanItems?: CarePlanResponse[]
  careEvents?: CareEventsResponse[]
}

export interface CareTeamResponse {
  spetialty?: string
  color?: string
  fullName?: string
  phone?: string
  /** @format date-time */
  lastSeen?: string | null
  order?: string | null
  orderResult?: string | null
}

export interface CarePlanResponse {
  id?: string
  /** @format date-time */
  date?: string
  isConfirmed?: boolean
  practitioner?: string
  title?: string
}

export interface CareEventsResponse {
  /** @format date-time */
  date?: string
  type?: string
  practitioner?: EventPractitionerResponse | null
  problem?: ProblemEventResponse | null
  medication?: MedicationEventResponse | null
  lab?: LabEventResponse | null
}

export interface EventPractitionerResponse {
  id?: string
  name?: string
  color?: string
  specialtyClassification?: string
  phone?: string
}

export interface ProblemEventResponse {
  conditionDescription?: string
  clinicalStatus?: string
  verificationStatus?: string
  /** @format date */
  diagnosisDate?: string
  /** @format date */
  abatement?: string | null
}

export interface MedicationEventResponse {
  name?: string | null
  status?: string | null
  frequency?: string
  customFrequency?: string | null
  duration?: string
  prn?: boolean | null
  prnReason?: string | null
  medRequest?: string | null
  encounterNote?: string | null
  orderId?: string | null
  bundleId?: string | null
}

export interface LabEventResponse {
  name?: string | null
  subType?: string | null
  status?: string | null
  result?: string | null
}

export interface PaginatedListOfQuestionnaireResponse {
  items?: QuestionnaireResponse[]
  /** @format int32 */
  pageNumber?: number
  /** @format int32 */
  pageSize?: number
  /** @format int64 */
  totalItems?: number
  /** @format int32 */
  totalPages?: number
}

export interface QuestionnaireResponse {
  id?: string
  title?: string
  locationName?: string
  organizationName?: string
  classification?: string
  placement?: string
  isFromLocation?: boolean
}

export interface QuestionnaireDetailsResponse {
  id?: string
  title?: string
  classification?: string
  placement?: string
  questions?: QuestionResponse2[]
}

export interface QuestionResponse2 {
  id?: string
  text?: string
  type?: string
  isRequired?: boolean
  options?: string[] | null
  answers?: string[]
}

export interface PatientQuestionnaireResponse {
  id?: string
  title?: string
  locationName?: string
  organizationName?: string
  questions?: QuestionResponse2[]
}

export type CreateQuestionnaireCommand = AuthRequestOfUnit & {
  title?: string
  classification?: string
  placement?: string
  questions?: QuestionRequest[]
  isGeneral?: boolean
}

export interface QuestionRequest {
  id?: string | null
  text?: string
  type?: string
  isRequired?: boolean
  options?: string[] | null
}

export type UpdateQuestionnaireCommand = AuthRequestOfUnit & {
  id?: string
  title?: string
  classification?: string
  placement?: string
  questions?: QuestionRequest[]
}

export type SaveQuestionnaireResponseCommand = AuthRequestOfUnit & {
  questionnaireId?: string
  encounterId?: string | null
  answers?: QuestionAnswer[]
}

export interface QuestionAnswer {
  questionId?: string
  answers?: string[]
}

export interface FormatTextResponse {
  formattedText?: string
}

export type FormatTextCommand = AuthRequestOfFormatTextResponse & {
  text?: string
}

export interface AuthRequestOfFormatTextResponse {
  /** @format date-time */
  timestamp?: string
}

export interface TranscribeAudioResponse {
  transcribedText?: string
}

export interface TranscribeAndFormatAudioResponse {
  rawTranscription?: string
  formattedText?: string
}

interface BaseOrderResponse {
  id?: string
  name?: string
  priority?: string
  status?: string
  orderEntry?: SearchOrderEntryResponse
  /** @format date-time */
  completedAt?: string | null
  orderType: string
}

type BaseOrderResponseOrderTypeMapping<Key, Type> = {
  orderType: Key
} & Type

interface BaseOrderTemplateEntryResponse {
  id?: string
  name?: string
  isRequired?: boolean
  orderEntry?: SearchOrderEntryResponse
  orderType: string
}

type BaseOrderTemplateEntryResponseOrderTypeMapping<Key, Type> = {
  orderType: Key
} & Type

import type {
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  HeadersDefaults,
  ResponseType,
} from 'axios'
import axios from 'axios'

export type QueryParamsType = Record<string | number, any>

export interface FullRequestParams
  extends Omit<AxiosRequestConfig, 'data' | 'params' | 'url' | 'responseType'> {
  /** set parameter to `true` for call `securityWorker` for this request */
  secure?: boolean
  /** request path */
  path: string
  /** content type of request body */
  type?: ContentType
  /** query params */
  query?: QueryParamsType
  /** format of response (i.e. response.json() -> format: "json") */
  format?: ResponseType
  /** request body */
  body?: unknown
}

export type RequestParams = Omit<FullRequestParams, 'body' | 'method' | 'query' | 'path'>

export interface ApiConfig<SecurityDataType = unknown>
  extends Omit<AxiosRequestConfig, 'data' | 'cancelToken'> {
  securityWorker?: (
    securityData: SecurityDataType | null,
  ) => Promise<AxiosRequestConfig | void> | AxiosRequestConfig | void
  secure?: boolean
  format?: ResponseType
}

export enum ContentType {
  Json = 'application/json',
  FormData = 'multipart/form-data',
  UrlEncoded = 'application/x-www-form-urlencoded',
  Text = 'text/plain',
}

export class HttpClient<SecurityDataType = unknown> {
  public instance: AxiosInstance
  private securityData: SecurityDataType | null = null
  private securityWorker?: ApiConfig<SecurityDataType>['securityWorker']
  private secure?: boolean
  private format?: ResponseType

  constructor({
    securityWorker,
    secure,
    format,
    ...axiosConfig
  }: ApiConfig<SecurityDataType> = {}) {
    this.instance = axios.create({
      ...axiosConfig,
      baseURL: axiosConfig.baseURL || 'http://localhost:5233',
    })
    this.secure = secure
    this.format = format
    this.securityWorker = securityWorker
  }

  public setSecurityData = (data: SecurityDataType | null) => {
    this.securityData = data
  }

  protected mergeRequestParams(
    params1: AxiosRequestConfig,
    params2?: AxiosRequestConfig,
  ): AxiosRequestConfig {
    const method = params1.method || (params2 && params2.method)

    return {
      ...this.instance.defaults,
      ...params1,
      ...(params2 || {}),
      headers: {
        ...((method &&
          this.instance.defaults.headers[method.toLowerCase() as keyof HeadersDefaults]) ||
          {}),
        ...(params1.headers || {}),
        ...((params2 && params2.headers) || {}),
      },
    }
  }

  protected stringifyFormItem(formItem: unknown) {
    if (typeof formItem === 'object' && formItem !== null) {
      return JSON.stringify(formItem)
    } else {
      return `${formItem}`
    }
  }

  protected createFormData(input: Record<string, unknown>): FormData {
    if (input instanceof FormData) {
      return input
    }
    return Object.keys(input || {}).reduce((formData, key) => {
      const property = input[key]
      const propertyContent: any[] = property instanceof Array ? property : [property]

      for (const formItem of propertyContent) {
        const isFileType = formItem instanceof Blob || formItem instanceof File
        formData.append(key, isFileType ? formItem : this.stringifyFormItem(formItem))
      }

      return formData
    }, new FormData())
  }

  public request = async <T = any, _E = any>({
    secure,
    path,
    type,
    query,
    format,
    body,
    ...params
  }: FullRequestParams): Promise<AxiosResponse<T>> => {
    const secureParams =
      ((typeof secure === 'boolean' ? secure : this.secure) &&
        this.securityWorker &&
        (await this.securityWorker(this.securityData))) ||
      {}
    const requestParams = this.mergeRequestParams(params, secureParams)
    const responseFormat = format || this.format || undefined

    if (type === ContentType.FormData && body && body !== null && typeof body === 'object') {
      body = this.createFormData(body as Record<string, unknown>)
    }

    if (type === ContentType.Text && body && body !== null && typeof body !== 'string') {
      body = JSON.stringify(body)
    }

    return this.instance.request({
      ...requestParams,
      headers: {
        ...(requestParams.headers || {}),
        ...(type ? { 'Content-Type': type } : {}),
      },
      params: query,
      responseType: responseFormat,
      data: body,
      url: path,
    })
  }
}

/**
 * @title My Title
 * @version 1.0.0
 * @baseUrl http://localhost:5233
 */
export class Api<SecurityDataType extends unknown> extends HttpClient<SecurityDataType> {
  allergies = {
    /**
     * No description
     *
     * @tags Allergie
     * @name AllergieListAllergiess
     * @request GET:/allergies
     */
    allergieListAllergiess: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfCodingResponse, any>({
        path: `/allergies`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Allergie
     * @name AllergieImport
     * @request POST:/allergies
     */
    allergieImport: (
      data: {
        /** @format binary */
        File?: File | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/allergies`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),
  }
  appointments = {
    /**
     * No description
     *
     * @tags Appointment
     * @name AppointmentListPatientAppointments
     * @request GET:/appointments
     */
    appointmentListPatientAppointments: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
        timeFilter?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfAppointmentResponse, any>({
        path: `/appointments`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Appointment
     * @name AppointmentEditAppointment
     * @request PUT:/appointments
     */
    appointmentEditAppointment: (data: EditAppointmentCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/appointments`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Appointment
     * @name AppointmentCancelAppointment
     * @request DELETE:/appointments
     */
    appointmentCancelAppointment: (data: CancelAppointmentCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/appointments`,
        method: 'DELETE',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Appointment
     * @name AppointmentListCalendarAppointments
     * @request GET:/appointments/calendar-appointments
     */
    appointmentListCalendarAppointments: (
      query?: {
        employeeId?: string | null
        /** @format date-time */
        start?: string | null
        /** @format date-time */
        end?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<CalendarAppointmentResponse[], any>({
        path: `/appointments/calendar-appointments`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Appointment
     * @name AppointmentAppointmentById
     * @request GET:/appointments/{id}
     */
    appointmentAppointmentById: (id: string, params: RequestParams = {}) =>
      this.request<AppointmentDetailsResponse, any>({
        path: `/appointments/${id}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Appointment
     * @name AppointmentCreateForNewPatient
     * @request POST:/appointments/new-patient
     */
    appointmentCreateForNewPatient: (
      data: CreateNewPatientAppointmentCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/appointments/new-patient`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Appointment
     * @name AppointmentCreateForExistingPatient
     * @request POST:/appointments/existing-patient
     */
    appointmentCreateForExistingPatient: (
      data: CreateExistingPatientAppointmentCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/appointments/existing-patient`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Appointment
     * @name AppointmentMarkAsCheckedIn
     * @request POST:/appointments/mark-checked-in
     */
    appointmentMarkAsCheckedIn: (
      data: MarkAppointmentAsCheckedInCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/appointments/mark-checked-in`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Appointment
     * @name AppointmentConfirm
     * @request POST:/appointments/confirm
     */
    appointmentConfirm: (data: ConfirmAppointmentCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/appointments/confirm`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Appointment
     * @name AppointmentCheckExistingAppointment
     * @request GET:/appointments/check-existing
     */
    appointmentCheckExistingAppointment: (
      query?: {
        patientId?: string
        employeeId?: string
        /** @format date-time */
        date?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<ExistingAppointmentResponse, any>({
        path: `/appointments/check-existing`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),
  }
  authentication = {
    /**
     * No description
     *
     * @tags Authentication
     * @name AuthenticationLoginUserPost
     * @request POST:/authentication/login
     */
    authenticationLoginUserPost: (data: LoginUserCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/authentication/login`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Authentication
     * @name AuthenticationLoginUserGet
     * @request GET:/authentication/user
     */
    authenticationLoginUserGet: (params: RequestParams = {}) =>
      this.request<UserInfoResponse, any>({
        path: `/authentication/user`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Authentication
     * @name AuthenticationSetPasswordEmployee
     * @request POST:/authentication/set-password-employee
     */
    authenticationSetPasswordEmployee: (
      data: SetPasswordEmployeeCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/authentication/set-password-employee`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Authentication
     * @name AuthenticationSetPasswordPatient
     * @request POST:/authentication/set-password-patient
     */
    authenticationSetPasswordPatient: (
      data: SetPasswordPatientCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/authentication/set-password-patient`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Authentication
     * @name AuthenticationSeed
     * @request POST:/authentication/seed
     */
    authenticationSeed: (params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/authentication/seed`,
        method: 'POST',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Authentication
     * @name AuthenticationUpdateSession
     * @request POST:/authentication/update-session
     */
    authenticationUpdateSession: (data: UpdateSessionCommand, params: RequestParams = {}) =>
      this.request<Unit, any>({
        path: `/authentication/update-session`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),
  }
  cptcodes = {
    /**
     * No description
     *
     * @tags CptCodes
     * @name CptCodesListCptCodes
     * @request GET:/cptcodes
     */
    cptCodesListCptCodes: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfCptCodeResponse, any>({
        path: `/cptcodes`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags CptCodes
     * @name CptCodesImport
     * @request POST:/cptcodes
     */
    cptCodesImport: (
      data: {
        /** @format binary */
        File?: File | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/cptcodes`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),
  }
  email = {
    /**
     * No description
     *
     * @tags EmailInbound
     * @name EmailInboundReceiveEmail
     * @request POST:/email/inbound
     */
    emailInboundReceiveEmail: (params: RequestParams = {}) =>
      this.request<File, any>({
        path: `/email/inbound`,
        method: 'POST',
        ...params,
      }),
  }
  employees = {
    /**
     * No description
     *
     * @tags Employee
     * @name EmployeeListEmployees
     * @request GET:/employees
     */
    employeeListEmployees: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfEmployeeResponse, any>({
        path: `/employees`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Employee
     * @name EmployeeCreate
     * @request POST:/employees
     */
    employeeCreate: (data: CreateEmployeeCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/employees`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Employee
     * @name EmployeeEdit
     * @request PUT:/employees
     */
    employeeEdit: (data: EditEmployeeCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/employees`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Employee
     * @name EmployeeListEmployeesLookup
     * @request GET:/employees/lookup
     */
    employeeListEmployeesLookup: (params: RequestParams = {}) =>
      this.request<SelectListItem[], any>({
        path: `/employees/lookup`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Employee
     * @name EmployeeGetLocationEmployee
     * @request GET:/employees/{employeeId}/locations/{locationId}
     */
    employeeGetLocationEmployee: (
      employeeId: string,
      locationId: string,
      params: RequestParams = {},
    ) =>
      this.request<LocationEmployeeResponse, any>({
        path: `/employees/${employeeId}/locations/${locationId}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Employee
     * @name EmployeeEditLocationEmployeeGeneralSettings
     * @request PUT:/employees/{employeeId}/locations/{locationId}/general-settings
     */
    employeeEditLocationEmployeeGeneralSettings: (
      employeeId: string,
      locationId: string,
      data: EditGeneralSettingsRequest,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/employees/${employeeId}/locations/${locationId}/general-settings`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Employee
     * @name EmployeeEditOrganizationEmployeeOfficeHours
     * @request PUT:/employees/{employeeId}/locations/{locationId}/office-hours
     */
    employeeEditOrganizationEmployeeOfficeHours: (
      employeeId: string,
      locationId: string,
      data: EditOfficeHoursRequest,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/employees/${employeeId}/locations/${locationId}/office-hours`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Employee
     * @name EmployeeEditLocationEmployeeOutOfOfficeHours
     * @request PUT:/employees/{employeeId}/locations/{locationId}/out-of-office-hours
     */
    employeeEditLocationEmployeeOutOfOfficeHours: (
      employeeId: string,
      locationId: string,
      data: EditOutOfOfficeHoursRequest,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/employees/${employeeId}/locations/${locationId}/out-of-office-hours`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Employee
     * @name EmployeeGetAvailableAppointmentSlots
     * @request GET:/employees/{employeeId}/locations/{locationId}/appointment-slots
     */
    employeeGetAvailableAppointmentSlots: (
      employeeId: string,
      locationId: string,
      query?: {
        /** @format date-time */
        date?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<AvailableAppointmentSlotsResponse, any>({
        path: `/employees/${employeeId}/locations/${locationId}/appointment-slots`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),
  }
  encounter = {
    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterGetEncounterById
     * @request GET:/encounter/{id}
     */
    encounterGetEncounterById: (id: string, params: RequestParams = {}) =>
      this.request<EncounterDetailsResponse, any>({
        path: `/encounter/${id}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterListActiveEncounters
     * @request GET:/encounter/active
     */
    encounterListActiveEncounters: (params: RequestParams = {}) =>
      this.request<EncounterResponse[], any>({
        path: `/encounter/active`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterGetLatestPatientEncounter
     * @request GET:/encounter/latest/{patientId}
     */
    encounterGetLatestPatientEncounter: (patientId: string, params: RequestParams = {}) =>
      this.request<EncounterResponse, any>({
        path: `/encounter/latest/${patientId}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterGetActiveEncounters
     * @request GET:/encounter
     */
    encounterGetActiveEncounters: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
        showAll?: boolean
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfActiveEncounterResponse, any>({
        path: `/encounter`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterUpdateScratchText
     * @request POST:/encounter/scratch
     */
    encounterUpdateScratchText: (data: UpdateScratchTextCommand, params: RequestParams = {}) =>
      this.request<Unit, any>({
        path: `/encounter/scratch`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterAddVitalSigns
     * @request POST:/encounter/vitals
     */
    encounterAddVitalSigns: (data: AddVitalSignsCommand, params: RequestParams = {}) =>
      this.request<Unit, any>({
        path: `/encounter/vitals`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterListVitalSigns
     * @request GET:/encounter/vitals
     */
    encounterListVitalSigns: (data: AddVitalSignsCommand, params: RequestParams = {}) =>
      this.request<VitalSignResponse, any>({
        path: `/encounter/vitals`,
        method: 'GET',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterGetEncounterQuestionnaires
     * @request GET:/encounter/{id}/questionnaires
     */
    encounterGetEncounterQuestionnaires: (id: string, params: RequestParams = {}) =>
      this.request<EncounterQuestionnaireResponse[], any>({
        path: `/encounter/${id}/questionnaires`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterMarkEncounterNoteCompleted
     * @request GET:/encounter/notes/{id}
     */
    encounterMarkEncounterNoteCompleted: (id: string, params: RequestParams = {}) =>
      this.request<Unit, any>({
        path: `/encounter/notes/${id}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterCreatePatientProblem
     * @request POST:/encounter/problems
     */
    encounterCreatePatientProblem: (
      data: CreatePatientProblemCommand,
      params: RequestParams = {},
    ) =>
      this.request<File, any>({
        path: `/encounter/problems`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterUpdateStatus
     * @request POST:/encounter/update-status
     */
    encounterUpdateStatus: (data: UpdateEncounterStatusCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/encounter/update-status`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterCheckoutEncounter
     * @request POST:/encounter/checkout
     */
    encounterCheckoutEncounter: (data: CheckoutEncounterCommand, params: RequestParams = {}) =>
      this.request<Unit, any>({
        path: `/encounter/checkout`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterGetCommunications
     * @request GET:/encounter/{encounterId}/communication
     */
    encounterGetCommunications: (encounterId: string, params: RequestParams = {}) =>
      this.request<EncounterCommunicationMessageResponse[], any>({
        path: `/encounter/${encounterId}/communication`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterSendEncounterMessage
     * @request POST:/encounter/{encounterId}/communication
     */
    encounterSendEncounterMessage: (
      encounterId: string,
      data: {
        MessageType?: string | null
        EncounterId?: string | null
        Subject?: string | null
        Message?: string | null
        Attachments?: File[] | null
        SentByPatient?: boolean
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/communication`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterSendMessageToPractitioner
     * @request POST:/encounter/{encounterId}/communication/reply
     */
    encounterSendMessageToPractitioner: (
      encounterId: string,
      data: {
        MessageId?: string | null
        EncounterId?: string | null
        Subject?: string | null
        Message?: string | null
        Attachments?: File[] | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/communication/reply`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterMarkMessageAsSeen
     * @request POST:/encounter/{encounterId}/communication/seen
     */
    encounterMarkMessageAsSeen: (
      encounterId: string,
      data: {
        MessageId?: string | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/communication/seen`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterUploadFileForEncounterCommunication
     * @request POST:/encounter/{encounterId}/communication/upload
     */
    encounterUploadFileForEncounterCommunication: (
      encounterId: string,
      data: {
        /** @format binary */
        Document?: File | null
        PatientId?: string | null
        EncounterId?: string | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/encounter/${encounterId}/communication/upload`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterSearchOrderEntry
     * @request GET:/encounter/{encounterId}/orders/seach
     */
    encounterSearchOrderEntry: (
      encounterId: string,
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfSearchOrderEntryResponse, any>({
        path: `/encounter/${encounterId}/orders/seach`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterGetOrders
     * @request POST:/encounter/{encounterId}/orders
     */
    encounterGetOrders: (encounterId: string, params: RequestParams = {}) =>
      this.request<OrderResponse[], any>({
        path: `/encounter/${encounterId}/orders`,
        method: 'POST',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterCreateOrderMedication
     * @request POST:/encounter/{encounterId}/order-medications
     */
    encounterCreateOrderMedication: (
      encounterId: string,
      data: CreateOrderMedicationCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/encounter/${encounterId}/order-medications`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterEditOrderMedication
     * @request PUT:/encounter/{encounterId}/order-medications
     */
    encounterEditOrderMedication: (
      encounterId: string,
      data: EditOrderMedicationCommand,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/order-medications`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterCreateOrderLab
     * @request POST:/encounter/{encounterId}/order-labs
     */
    encounterCreateOrderLab: (
      encounterId: string,
      data: CreateOrderLabCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/encounter/${encounterId}/order-labs`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterEditOrderLab
     * @request PUT:/encounter/{encounterId}/order-labs
     */
    encounterEditOrderLab: (
      encounterId: string,
      data: EditOrderLabCommand,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/order-labs`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterCreateOrderProcedure
     * @request POST:/encounter/{encounterId}/order-procedures
     */
    encounterCreateOrderProcedure: (
      encounterId: string,
      data: CreateOrderProcedureCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/encounter/${encounterId}/order-procedures`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterEditOrderProcedure
     * @request PUT:/encounter/{encounterId}/order-procedures
     */
    encounterEditOrderProcedure: (
      encounterId: string,
      data: EditOrderProcedureCommand,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/order-procedures`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterCreateOrderBundle
     * @request POST:/encounter/{encounterId}/order-bundle
     */
    encounterCreateOrderBundle: (
      encounterId: string,
      data: CreateOrderBundleCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/encounter/${encounterId}/order-bundle`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterEditOrderBundle
     * @request PUT:/encounter/{encounterId}/order-bundle
     */
    encounterEditOrderBundle: (
      encounterId: string,
      data: EditOrderBundleCommand,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/order-bundle`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterChangeOrderStatus
     * @request PUT:/encounter/{encounterId}/order/status
     */
    encounterChangeOrderStatus: (
      encounterId: string,
      data: ChangeOrderStatusCommand,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/order/status`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterSaveOrdersAsBundle
     * @request PUT:/encounter/{encounterId}/order/save-as-bundle
     */
    encounterSaveOrdersAsBundle: (
      encounterId: string,
      data: SaveOrdersAsBundleCommand,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/order/save-as-bundle`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterDeleteOrder
     * @request DELETE:/encounter/{encounterId}/order
     */
    encounterDeleteOrder: (
      encounterId: string,
      data: DeleteOrderCommand,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/order`,
        method: 'DELETE',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterGetMedicationRecordByOrderId
     * @request GET:/encounter/{encounterId}/order-medications/{orderId}
     */
    encounterGetMedicationRecordByOrderId: (
      encounterId: string,
      orderId: string,
      query?: {
        bundleId?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<MedicationRecordResponse, any>({
        path: `/encounter/${encounterId}/order-medications/${orderId}`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterChangeOrderTimingStatus
     * @request PUT:/encounter/{encounterId}/order/timing-status
     */
    encounterChangeOrderTimingStatus: (
      encounterId: string,
      data: ChangeOrderTimingStatusCommand,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/order/timing-status`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterChangeOrderNote
     * @request PUT:/encounter/{encounterId}/order/note
     */
    encounterChangeOrderNote: (
      encounterId: string,
      data: ChangeOrderNoteCommand,
      params: RequestParams = {},
    ) =>
      this.request<Unit, any>({
        path: `/encounter/${encounterId}/order/note`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterChargeEncounterIpos
     * @request POST:/encounter/billing/ipos
     */
    encounterChargeEncounterIpos: (data: ProcessPaymentIposCommand, params: RequestParams = {}) =>
      this.request<ProcessPaymentResponse, any>({
        path: `/encounter/billing/ipos`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterChargeEncounterIposCloud
     * @request POST:/encounter/billing/ipos-cloud
     */
    encounterChargeEncounterIposCloud: (
      data: ProcessPaymentIposCloudCommand,
      params: RequestParams = {},
    ) =>
      this.request<ProcessPaymentResponse, any>({
        path: `/encounter/billing/ipos-cloud`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterBrowseEncountersBilling
     * @request GET:/encounter/billing
     */
    encounterBrowseEncountersBilling: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
        status?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfEncountersBillingResponse, any>({
        path: `/encounter/billing`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterListEncounterTransactions
     * @request GET:/encounter/{id}/transactions
     */
    encounterListEncounterTransactions: (id: string, params: RequestParams = {}) =>
      this.request<EncounterTransactionResponse[], any>({
        path: `/encounter/${id}/transactions`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterVoidOrRefundTotal
     * @request GET:/encounter/transactions/{id}
     */
    encounterVoidOrRefundTotal: (id: string, params: RequestParams = {}) =>
      this.request<ProcessPaymentResponse[], any>({
        path: `/encounter/transactions/${id}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Encounter
     * @name EncounterCustomRefund
     * @request POST:/encounter/transactions/{id}/custom-refund
     */
    encounterCustomRefund: (id: string, data: CustomRefundCommand, params: RequestParams = {}) =>
      this.request<ProcessPaymentResponse, any>({
        path: `/encounter/transactions/${id}/custom-refund`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),
  }
  icd10 = {
    /**
     * No description
     *
     * @tags Icd10
     * @name Icd10ListIcd10
     * @request GET:/icd10
     */
    icd10ListIcd10: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfCodingResponse, any>({
        path: `/icd10`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Icd10
     * @name Icd10Import
     * @request POST:/icd10
     */
    icd10Import: (
      data: {
        /** @format binary */
        File?: File | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/icd10`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),
  }
  immunizations = {
    /**
     * No description
     *
     * @tags Immunization
     * @name ImmunizationListImmunizations
     * @request GET:/immunizations
     */
    immunizationListImmunizations: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfCodingResponse, any>({
        path: `/immunizations`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Immunization
     * @name ImmunizationImport
     * @request POST:/immunizations
     */
    immunizationImport: (
      data: {
        /** @format binary */
        File?: File | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/immunizations`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),
  }
  locations = {
    /**
     * No description
     *
     * @tags Location
     * @name LocationListLocations
     * @request GET:/locations
     */
    locationListLocations: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfLocationResponse, any>({
        path: `/locations`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Location
     * @name LocationCreate
     * @request POST:/locations
     */
    locationCreate: (data: CreateLocationCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/locations`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Location
     * @name LocationEdit
     * @request PUT:/locations
     */
    locationEdit: (data: EditLocationCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/locations`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Location
     * @name LocationDeactivate
     * @request DELETE:/locations
     */
    locationDeactivate: (data: DeactivateLocationCommand, params: RequestParams = {}) =>
      this.request<File, any>({
        path: `/locations`,
        method: 'DELETE',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags Location
     * @name LocationListLocationsLookup
     * @request GET:/locations/lookup
     */
    locationListLocationsLookup: (params: RequestParams = {}) =>
      this.request<SelectListItem[], any>({
        path: `/locations/lookup`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Location
     * @name LocationGetLocationsWithDoctors
     * @request GET:/locations/locations-with-doctors
     */
    locationGetLocationsWithDoctors: (params: RequestParams = {}) =>
      this.request<LocationWithPractitionersResponse[], any>({
        path: `/locations/locations-with-doctors`,
        method: 'GET',
        format: 'json',
        ...params,
      }),
  }
  loinc = {
    /**
     * No description
     *
     * @tags LoincCode
     * @name LoincCodeListLoincCodes
     * @request GET:/loinc
     */
    loincCodeListLoincCodes: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfCodingResponse, any>({
        path: `/loinc`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags LoincCode
     * @name LoincCodeImport
     * @request POST:/loinc
     */
    loincCodeImport: (
      data: {
        /** @format binary */
        File?: File | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/loinc`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags LoincCode
     * @name LoincCodeGetLoincCodeById
     * @request GET:/loinc/{id}
     */
    loincCodeGetLoincCodeById: (id: string, params: RequestParams = {}) =>
      this.request<LoincCodeDetailsResponse, any>({
        path: `/loinc/${id}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),
  }
  medications = {
    /**
     * No description
     *
     * @tags Medication
     * @name MedicationListMedications
     * @request GET:/medications
     */
    medicationListMedications: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfCodingResponse, any>({
        path: `/medications`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Medication
     * @name MedicationImport
     * @request POST:/medications
     */
    medicationImport: (
      data: {
        /** @format binary */
        File?: File | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/medications`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Medication
     * @name MedicationGetMedicationsByIngredient
     * @request GET:/medications/ingredient/{ingredientId}
     */
    medicationGetMedicationsByIngredient: (ingredientId: string, params: RequestParams = {}) =>
      this.request<SelectListItem[], any>({
        path: `/medications/ingredient/${ingredientId}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),
  }
  notes = {
    /**
     * No description
     *
     * @tags Note
     * @name NoteListPatientNotes
     * @request GET:/notes/patientId
     */
    noteListPatientNotes: (
      query?: {
        patientId?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<PatientNoteResponse[], any>({
        path: `/notes/patientId`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Note
     * @name NoteListPatientNotesVersions
     * @request GET:/notes/{id}/versions
     */
    noteListPatientNotesVersions: (id: string, params: RequestParams = {}) =>
      this.request<PatientNoteResponse[], any>({
        path: `/notes/${id}/versions`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Note
     * @name NoteCreateNote
     * @request POST:/notes
     */
    noteCreateNote: (data: CreateNoteCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/notes`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Note
     * @name NoteAmendNote
     * @request PUT:/notes
     */
    noteAmendNote: (data: AmendNoteCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/notes`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),
  }
  noteTemplates = {
    /**
     * No description
     *
     * @tags NoteTemplate
     * @name NoteTemplateListNoteTemplates
     * @request GET:/note-templates
     */
    noteTemplateListNoteTemplates: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
        specialtyFilter?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfNoteTemplateResponse, any>({
        path: `/note-templates`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags NoteTemplate
     * @name NoteTemplateCreateNoteTemplate
     * @request POST:/note-templates
     */
    noteTemplateCreateNoteTemplate: (data: CreateNoteTemplateCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/note-templates`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags NoteTemplate
     * @name NoteTemplateUpdateNoteTemplate
     * @request PUT:/note-templates
     */
    noteTemplateUpdateNoteTemplate: (data: UpdateNoteTemplateCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/note-templates`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags NoteTemplate
     * @name NoteTemplateGetNoteTemplate
     * @request GET:/note-templates/{id}
     */
    noteTemplateGetNoteTemplate: (id: string, params: RequestParams = {}) =>
      this.request<NoteTemplateDetailsResponse, any>({
        path: `/note-templates/${id}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),
  }
  notifications = {
    /**
     * No description
     *
     * @tags Notification
     * @name NotificationGetUserNotifications
     * @request GET:/notifications
     */
    notificationGetUserNotifications: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfNotificationResponse, any>({
        path: `/notifications`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Notification
     * @name NotificationMarkNotificationAsRead
     * @request PUT:/notifications/{id}/read
     */
    notificationMarkNotificationAsRead: (id: string, params: RequestParams = {}) =>
      this.request<File, any>({
        path: `/notifications/${id}/read`,
        method: 'PUT',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Notification
     * @name NotificationDeleteNotification
     * @request DELETE:/notifications/{id}
     */
    notificationDeleteNotification: (id: string, params: RequestParams = {}) =>
      this.request<File, any>({
        path: `/notifications/${id}`,
        method: 'DELETE',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Notification
     * @name NotificationGetUnreadNotificationCount
     * @request GET:/notifications/unread-count
     */
    notificationGetUnreadNotificationCount: (params: RequestParams = {}) =>
      this.request<number, any>({
        path: `/notifications/unread-count`,
        method: 'GET',
        format: 'json',
        ...params,
      }),
  }
  bundleTemplates = {
    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateGetOrderBundleTemplateList
     * @request GET:/bundle-templates
     */
    orderBundleTemplateGetOrderBundleTemplateList: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfOrderBundleTemplateListResponse, any>({
        path: `/bundle-templates`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateCreateOrderBundleTemplate
     * @request POST:/bundle-templates
     */
    orderBundleTemplateCreateOrderBundleTemplate: (
      data: CreateOrderBundleTemplateCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateEditOrderBundleTemplate
     * @request PUT:/bundle-templates
     */
    orderBundleTemplateEditOrderBundleTemplate: (
      data: EditOrderBundleTemplateCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateGetOrderBundleTemplate
     * @request GET:/bundle-templates/{id}
     */
    orderBundleTemplateGetOrderBundleTemplate: (id: string, params: RequestParams = {}) =>
      this.request<OrderBundleTemplateResponse, any>({
        path: `/bundle-templates/${id}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateAddLabOrderEntryToBundleTemplate
     * @request POST:/bundle-templates/lab-order-entry
     */
    orderBundleTemplateAddLabOrderEntryToBundleTemplate: (
      data: AddLabOrderEntryToBundleTemplateCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates/lab-order-entry`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateEditLabOrderEntryInBundleTemplate
     * @request PUT:/bundle-templates/lab-order-entry
     */
    orderBundleTemplateEditLabOrderEntryInBundleTemplate: (
      data: EditLabOrderEntryInBundleTemplateCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates/lab-order-entry`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateAddProcedureOrderEntryToBundleTemplate
     * @request POST:/bundle-templates/procedure-order-entry
     */
    orderBundleTemplateAddProcedureOrderEntryToBundleTemplate: (
      data: AddProcedureOrderEntryToBundleTemplateCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates/procedure-order-entry`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateEditProcedureOrderEntryInBundleTemplate
     * @request PUT:/bundle-templates/procedure-order-entry
     */
    orderBundleTemplateEditProcedureOrderEntryInBundleTemplate: (
      data: EditProcedureOrderEntryInBundleTemplateCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates/procedure-order-entry`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateAddMedicationOrderEntryToBundleTemplate
     * @request POST:/bundle-templates/medication-order-entry
     */
    orderBundleTemplateAddMedicationOrderEntryToBundleTemplate: (
      data: AddMedicationOrderEntryToBundleTemplateCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates/medication-order-entry`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateEditMedicationOrderEntryInBundleTemplate
     * @request PUT:/bundle-templates/medication-order-entry
     */
    orderBundleTemplateEditMedicationOrderEntryInBundleTemplate: (
      data: EditMedicationOrderEntryInBundleTemplateCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates/medication-order-entry`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateAddBundleOrderEntryToBundleTemplate
     * @request POST:/bundle-templates/bundle-order-entry
     */
    orderBundleTemplateAddBundleOrderEntryToBundleTemplate: (
      data: AddBundleOrderEntryToBundleTemplateCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates/bundle-order-entry`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateDeleteOrderEntryFromBundleTemplate
     * @request DELETE:/bundle-templates/order-entry
     */
    orderBundleTemplateDeleteOrderEntryFromBundleTemplate: (
      data: DeleteOrderEntryFromBundleTemplateCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates/order-entry`,
        method: 'DELETE',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateAddOrderBundleTemplateStep
     * @request POST:/bundle-templates/steps
     */
    orderBundleTemplateAddOrderBundleTemplateStep: (
      data: AddOrderBundleTemplateStepCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates/steps`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateDeleteOrderBundleTemplateStep
     * @request DELETE:/bundle-templates/steps
     */
    orderBundleTemplateDeleteOrderBundleTemplateStep: (
      data: DeleteOrderBundleTemplateStepCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/bundle-templates/steps`,
        method: 'DELETE',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags OrderBundleTemplate
     * @name OrderBundleTemplateSearchOrderEntry
     * @request GET:/bundle-templates/order-entries
     */
    orderBundleTemplateSearchOrderEntry: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfSearchOrderEntryResponse, any>({
        path: `/bundle-templates/order-entries`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),
  }
  organizations = {
    /**
     * No description
     *
     * @tags Organization
     * @name OrganizationListOrganizations
     * @request GET:/organizations
     */
    organizationListOrganizations: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfOrganizationResponse, any>({
        path: `/organizations`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Organization
     * @name OrganizationCreate
     * @request POST:/organizations
     */
    organizationCreate: (data: CreateOrganizationCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/organizations`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Organization
     * @name OrganizationEdit
     * @request PUT:/organizations
     */
    organizationEdit: (data: EditOrganizationCommand, params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/organizations`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),
  }
  patients = {
    /**
     * No description
     *
     * @tags Patient
     * @name PatientListPatients
     * @request GET:/patients
     */
    patientListPatients: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfPatientResponse, any>({
        path: `/patients`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientGetPatientByMrn
     * @request GET:/patients/by-mrn/{mrn}
     */
    patientGetPatientByMrn: (mrn: string, params: RequestParams = {}) =>
      this.request<PatientResponse, any>({
        path: `/patients/by-mrn/${mrn}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientGetPatientProfile
     * @request GET:/patients/profile
     */
    patientGetPatientProfile: (
      query?: {
        patientId?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PatientProfileResponse, any>({
        path: `/patients/profile`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientSetPersonalInformation
     * @request POST:/patients/personal-info
     */
    patientSetPersonalInformation: (
      data: {
        PatientId?: string
        FirstName?: string
        LastName?: string
        MiddleName?: string | null
        Suffix?: string | null
        PreferredName?: string | null
        PreviousFirstName?: string | null
        PreviousLastName?: string | null
        PreferredLanguage?: string | null
        /** @format date-time */
        Birthday?: string
        BirthSex?: string
        GenderIdentity?: string | null
        SexualOrientation?: string | null
        Race?: string | null
        Ethnicity?: string | null
        TribalAffiliation?: string | null
        NewDocuments?: PatientDocument[]
        DeleteDocumentIds?: string[]
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/patients/personal-info`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientSetContactInformation
     * @request POST:/patients/contact-info
     */
    patientSetContactInformation: (
      data: SetPatientContactInfoCommand,
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/patients/contact-info`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientSetMedications
     * @request PUT:/patients/medications
     */
    patientSetMedications: (data: PatientMedicationRequest[], params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/patients/medications`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientSetAllergies
     * @request PUT:/patients/allergies
     */
    patientSetAllergies: (data: PatientAllergyRequest[], params: RequestParams = {}) =>
      this.request<string, any>({
        path: `/patients/allergies`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientListImmunizations
     * @request GET:/patients/immunizations
     */
    patientListImmunizations: (
      query?: {
        patientId?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PatientImmunizationResponse[], any>({
        path: `/patients/immunizations`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientAddImmunization
     * @request POST:/patients/immunizations
     */
    patientAddImmunization: (data: AddPatientImmunizationCommand, params: RequestParams = {}) =>
      this.request<File, any>({
        path: `/patients/immunizations`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientDeleteImmunization
     * @request DELETE:/patients/immunizations
     */
    patientDeleteImmunization: (
      data: DeletePatientImmunizationCommand,
      params: RequestParams = {},
    ) =>
      this.request<File, any>({
        path: `/patients/immunizations`,
        method: 'DELETE',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientListInsurances
     * @request GET:/patients/insurances
     */
    patientListInsurances: (
      query?: {
        patientId?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PatientInsuranceResponse, any>({
        path: `/patients/insurances`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientAddInsurance
     * @request POST:/patients/insurances
     */
    patientAddInsurance: (
      data: {
        Issuer?: string
        GroupId?: string | null
        MemberId?: string | null
        /** @format date */
        Start?: string
        /** @format date */
        End?: string | null
        Order?: string
        Type?: string
        /** @format decimal */
        Copay?: number | null
        /** @format decimal */
        Deductible?: number | null
        Relation?: string
        'Subscriber.FirstName'?: string | null
        'Subscriber.LastName'?: string | null
        /** @format date */
        'Subscriber.Birthday'?: string
        'Subscriber.BirthSex'?: string | null
        /** @format binary */
        CardFront?: File | null
        /** @format binary */
        CardBack?: File | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<File, any>({
        path: `/patients/insurances`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientDeleteInsurance
     * @request DELETE:/patients/insurances
     */
    patientDeleteInsurance: (data: DeletePatientInsuranceCommand, params: RequestParams = {}) =>
      this.request<File, any>({
        path: `/patients/insurances`,
        method: 'DELETE',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientGetInsuranceInfo
     * @request GET:/patients/insurance-info
     */
    patientGetInsuranceInfo: (
      query?: {
        patientId?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PatientInsuranceInfoResponse, any>({
        path: `/patients/insurance-info`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientListRecords
     * @request GET:/patients/records
     */
    patientListRecords: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfPatientRecordResponse, any>({
        path: `/patients/records`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientGetLaboratory
     * @request GET:/patients/{id}/laboratory
     */
    patientGetLaboratory: (id: string, params: RequestParams = {}) =>
      this.request<LaboratoryResultResponse[], any>({
        path: `/patients/${id}/laboratory`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientMarkLaboratoryResultAsSeen
     * @request PUT:/patients/laboratory
     */
    patientMarkLaboratoryResultAsSeen: (
      data: MarkLaboratoryResultAsSeenCommand,
      params: RequestParams = {},
    ) =>
      this.request<LaboratoryResultResponse[], any>({
        path: `/patients/laboratory`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientGetImagingResults
     * @request GET:/patients/{id}/imaging
     */
    patientGetImagingResults: (id: string, params: RequestParams = {}) =>
      this.request<ImagingResultResponse[], any>({
        path: `/patients/${id}/imaging`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientMarkImagingResultAsSeen
     * @request PUT:/patients/imaging
     */
    patientMarkImagingResultAsSeen: (
      data: MarkImagingResultAsSeenCommand,
      params: RequestParams = {},
    ) =>
      this.request<LaboratoryResultResponse[], any>({
        path: `/patients/imaging`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientListVitalSigns
     * @request GET:/patients/{id}/vital-signs
     */
    patientListVitalSigns: (id: string, params: RequestParams = {}) =>
      this.request<VitalSignResponse[], any>({
        path: `/patients/${id}/vital-signs`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientListPaymentCards
     * @request GET:/patients/{id}/payment-cards
     */
    patientListPaymentCards: (id: string, params: RequestParams = {}) =>
      this.request<PaymentCardResponse[], any>({
        path: `/patients/${id}/payment-cards`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientDeletePaymentCard
     * @request DELETE:/patients/payment-cards
     */
    patientDeletePaymentCard: (data: DeletePatientPaymentCardCommand, params: RequestParams = {}) =>
      this.request<File, any>({
        path: `/patients/payment-cards`,
        method: 'DELETE',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientListMessages
     * @request GET:/patients/messages
     */
    patientListMessages: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfPatientCommunicationMessageResponse, any>({
        path: `/patients/messages`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientListPatientActiveMedicationOrders
     * @request GET:/patients/{id}/active-medications
     */
    patientListPatientActiveMedicationOrders: (id: string, params: RequestParams = {}) =>
      this.request<PatientActiveMedication[], any>({
        path: `/patients/${id}/active-medications`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientListPatientProblems
     * @request GET:/patients/{id}/problems
     */
    patientListPatientProblems: (id: string, params: RequestParams = {}) =>
      this.request<PatientProblemResponse[], any>({
        path: `/patients/${id}/problems`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientCreatePatientProblem
     * @request POST:/patients/problems
     */
    patientCreatePatientProblem: (data: CreatePatientProblemCommand, params: RequestParams = {}) =>
      this.request<File, any>({
        path: `/patients/problems`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags Patient
     * @name PatientListPatientCare
     * @request GET:/patients/{id}/care
     */
    patientListPatientCare: (
      id: string,
      query?: {
        encounterId?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<PatientCareResponse[], any>({
        path: `/patients/${id}/care`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),
  }
  questionnaires = {
    /**
     * No description
     *
     * @tags Questionnaire
     * @name QuestionnaireListQuestionnaires
     * @request GET:/questionnaires
     */
    questionnaireListQuestionnaires: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
        locationFilter?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfQuestionnaireResponse, any>({
        path: `/questionnaires`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Questionnaire
     * @name QuestionnaireCreate
     * @request POST:/questionnaires
     */
    questionnaireCreate: (data: CreateQuestionnaireCommand, params: RequestParams = {}) =>
      this.request<File, any>({
        path: `/questionnaires`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags Questionnaire
     * @name QuestionnaireUpdate
     * @request PUT:/questionnaires
     */
    questionnaireUpdate: (data: UpdateQuestionnaireCommand, params: RequestParams = {}) =>
      this.request<File, any>({
        path: `/questionnaires`,
        method: 'PUT',
        body: data,
        type: ContentType.Json,
        ...params,
      }),

    /**
     * No description
     *
     * @tags Questionnaire
     * @name QuestionnaireGetQuestionnaire
     * @request GET:/questionnaires/{id}
     */
    questionnaireGetQuestionnaire: (id: string, params: RequestParams = {}) =>
      this.request<QuestionnaireDetailsResponse, any>({
        path: `/questionnaires/${id}`,
        method: 'GET',
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Questionnaire
     * @name QuestionnaireDeactivateQuestionnaire
     * @request DELETE:/questionnaires/{id}
     */
    questionnaireDeactivateQuestionnaire: (id: string, params: RequestParams = {}) =>
      this.request<File, any>({
        path: `/questionnaires/${id}`,
        method: 'DELETE',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Questionnaire
     * @name QuestionnaireListPatientQuestionnaires
     * @request GET:/questionnaires/patient
     */
    questionnaireListPatientQuestionnaires: (
      query?: {
        placement?: string
        encounterId?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PatientQuestionnaireResponse[], any>({
        path: `/questionnaires/patient`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags Questionnaire
     * @name QuestionnaireSaveResponse
     * @request POST:/questionnaires/response
     */
    questionnaireSaveResponse: (
      data: SaveQuestionnaireResponseCommand,
      params: RequestParams = {},
    ) =>
      this.request<File, any>({
        path: `/questionnaires/response`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        ...params,
      }),
  }
  snomed = {
    /**
     * No description
     *
     * @tags SnomedCode
     * @name SnomedCodeListSnomedCodes
     * @request GET:/snomed
     */
    snomedCodeListSnomedCodes: (
      query?: {
        /** @format int32 */
        pageNumber?: number | null
        /** @format int32 */
        pageSize?: number | null
        searchParam?: string | null
      },
      params: RequestParams = {},
    ) =>
      this.request<PaginatedListOfCodingResponse, any>({
        path: `/snomed`,
        method: 'GET',
        query: query,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags SnomedCode
     * @name SnomedCodeImport
     * @request POST:/snomed
     */
    snomedCodeImport: (
      data: {
        /** @format binary */
        File?: File | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<string, any>({
        path: `/snomed`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),
  }
  textFormatting = {
    /**
     * No description
     *
     * @tags TextFormatting
     * @name TextFormattingFormatText
     * @request POST:/text-formatting/format
     */
    textFormattingFormatText: (data: FormatTextCommand, params: RequestParams = {}) =>
      this.request<FormatTextResponse, any>({
        path: `/text-formatting/format`,
        method: 'POST',
        body: data,
        type: ContentType.Json,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags TextFormatting
     * @name TextFormattingTranscribeAudio
     * @request POST:/text-formatting/transcribe
     */
    textFormattingTranscribeAudio: (
      data: {
        /** @format binary */
        AudioFile?: File | null
        Model?: string | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<TranscribeAudioResponse, any>({
        path: `/text-formatting/transcribe`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),

    /**
     * No description
     *
     * @tags TextFormatting
     * @name TextFormattingTranscribeAndFormatAudio
     * @request POST:/text-formatting/transcribe-and-format
     */
    textFormattingTranscribeAndFormatAudio: (
      data: {
        /** @format binary */
        AudioFile?: File | null
        Model?: string | null
        /** @format date-time */
        Timestamp?: string
      },
      params: RequestParams = {},
    ) =>
      this.request<TranscribeAndFormatAudioResponse, any>({
        path: `/text-formatting/transcribe-and-format`,
        method: 'POST',
        body: data,
        type: ContentType.FormData,
        format: 'json',
        ...params,
      }),
  }
}
