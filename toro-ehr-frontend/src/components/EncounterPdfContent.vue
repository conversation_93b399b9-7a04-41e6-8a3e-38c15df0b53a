<template>
  <div class="px-8 py-8 text-sm text-gray-900 space-y-8 bg-white min-h-screen">
    <div class="mb-6 border-b pb-4">
      <h2 class="text-2xl font-bold text-gray-800 mb-2">
        {{ encounter?.patientName }} — Encounter on {{ formatDate(encounter?.encounterDate) }}
      </h2>
    </div>
    <div class="border border-gray-300 rounded bg-white">
      <div class="px-4 py-4">
        <h3 class="text-lg font-semibold mb-4 border-b pb-1">Patient Information</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-y-2 gap-x-4 text-sm text-gray-700">
          <div><span class="font-medium">Date of Birth:</span> {{ formatDate(encounter?.patientDateOfBirth) }}</div>
          <div><span class="font-medium">Sex:</span> {{ encounter?.patientSex }}</div>
          <div><span class="font-medium">Phone:</span> {{ encounter?.patientPhoneNumber }}</div>
          <div><span class="font-medium">Address:</span> {{ encounter?.patientAddress }}</div>
          <div><span class="font-medium">Location:</span> {{ encounter?.encounterLocationName }}</div>
          <div><span class="font-medium">Practitioner:</span> {{ encounter?.practitionerName }}</div>
        </div>
      </div>
    </div>

    <div class="space-y-10">
      <!-- Notes Card -->
      <div class="relative border border-gray-300 rounded bg-white">
        <div class="absolute left-0 top-0 h-full w-2 bg-gray-400 rounded-l"></div>
        <div class="px-4 py-4">
          <h3 class="text-lg font-semibold mb-2 border-b pb-1">Notes</h3>
          <div v-if="notes.length > 0 && notes.some(note => noteHasContent(note))">
            <template v-for="note in notes" :key="note.id">
              <div
                v-if="noteHasContent(note)"
                class="mb-4"
                style="page-break-inside: avoid; break-inside: avoid; display: block;"
              >
                <h4 class="font-semibold mb-1">{{ note.name || 'Note' }}</h4>
                <template v-for="field in note.fields || []" :key="field.name">
                  <div
                    v-if="field.value && field.value.trim() !== ''"
                    class="mb-2"
                    style="page-break-inside: avoid; break-inside: avoid; display: block;"
                  >
                    <p class="font-medium mt-6 mb-1 text-gray-800">{{ field.name }}</p>
                    <p class="border border-gray-300 p-2 whitespace-pre-line">{{ field.value }}</p>
                  </div>
                </template>
              </div>
            </template>
          </div>
          <div v-else class="text-gray-500 italic text-center py-4">
            No notes available for this encounter
          </div>
        </div>
      </div>

      <!-- Questionnaire Answers Card -->
      <div class="relative border border-gray-300 rounded bg-white">
        <div class="absolute left-0 top-0 h-full w-2 bg-gray-400 rounded-l"></div>
        <div class="px-4 py-4">
          <h3 class="text-lg font-semibold mb-2 border-b pb-1">Questionnaire Answers</h3>
          <div v-if="questionnaires.length > 0">
            <div v-for="q in questionnaires" :key="q.title" class="mb-4">
              <h4 class="font-semibold text-base mb-1">{{ q.title }}</h4>
              <div
                v-for="question in q.questions || []"
                :key="question.text"
                class="mb-2"
                style="page-break-inside: avoid; break-inside: avoid; display: block;"
              >
                <p class="font-medium">{{ question.text }}</p>
                <div v-if="question.answers?.length" class="flex flex-wrap gap-2 mt-1">
                  <span
                    v-for="a in question.answers"
                    :key="a"
                    class="bg-gray-100 text-gray-800 text-xs px-2 py-1 rounded-full"
                  >
                    {{ a }}
                  </span>
                </div>
                <p v-else class="italic text-gray-500">No answer provided</p>
              </div>
            </div>
          </div>
          <div v-else class="text-gray-500 italic text-center py-4">
            No questionnaire responses available for this encounter
          </div>
        </div>
      </div>

      <!-- Vital Signs Card -->
      <div class="relative border border-gray-300 rounded bg-white">
        <div class="absolute left-0 top-0 h-full w-2 bg-gray-400 rounded-l"></div>
        <div class="px-4 py-4">
          <h3 class="text-lg font-semibold mb-2 border-b pb-1">Vital Signs</h3>
          <div v-if="vitalSigns.length > 0">
            <div v-for="vs in vitalSigns" :key="vs.date" class="mb-4">
              <p class="text-sm text-gray-600 mb-1">{{ formatDateTime(vs.date) }}</p>
              <table class="w-full text-left text-sm border">
                <tbody>
                  <tr v-for="m in vs.measurements || []" :key="m.type" class="border-t">
                    <td class="font-medium pr-4 py-1">{{ m.type }}</td>
                    <td class="py-1">{{ m.value }} {{ getUnit(m.type || '') }}</td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          <div v-else class="text-gray-500 italic text-center py-4">
            No vital signs recorded for this encounter
          </div>
        </div>
      </div>

      <!-- Medications Card -->
      <div class="relative border border-gray-300 rounded bg-white">
        <div class="absolute left-0 top-0 h-full w-2 bg-gray-400 rounded-l"></div>
        <div class="px-4 py-4">
          <h3 class="text-lg font-semibold mb-2 border-b pb-1">Medications</h3>
          <div v-if="medications.length > 0">
            <div class="space-y-3">
              <div v-for="medication in medications" :key="medication.name" class="border-b border-gray-200 pb-2 last:border-b-0">
                <h4 class="font-semibold text-base mb-1">{{ medication.name }}</h4>
                <div class="text-sm text-gray-700">
                  <span v-if="medication.frequency" class="mr-4">
                    <span class="font-medium">Frequency:</span> {{ medication.frequency }}
                  </span>
                  <span v-if="medication.duration">
                    <span class="font-medium">Duration:</span> {{ medication.duration }}
                  </span>
                  <span v-if="medication.instructions">
                    <span class="font-medium">Instructions:</span> {{ medication.instructions }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-gray-500 italic text-center py-4">
            No medications prescribed for this encounter
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type {
  EncounterDetailsResponse,
  PatientNoteResponse
} from '@/api/api-reference'

const props = defineProps<{
  encounter: EncounterDetailsResponse | undefined
}>()

// computed properties to derive data from encounter
const questionnaires = computed(() => props.encounter?.questionnaires || [])
const notes = computed(() => props.encounter?.notes || [])
const vitalSigns = computed(() => props.encounter?.vitalSigns || [])
const medications = computed(() => props.encounter?.medications || [])

// helper function to check if a note has any content
const noteHasContent = (note: PatientNoteResponse) => {
  return note.fields && note.fields.some(field => field.value && field.value.trim() !== '')
}

// unit mapping for vital signs
const getUnit = (measurementType: string) => {
  const unitMap: Record<string, string> = {
    'SystolicBloodPressure': 'mmHg',
    'DiastolicBloodPressure': 'mmHg',
    'HeartRate': 'bpm',
    'BodyTemperature': '°F',
    'BodyWeight': 'lbs',
    'RespiratoryRate': '/min',
    'PainScale': '/10',
    'Height': 'in',
    'BMI': 'kg/m²',
    'OxygenSaturation': '%',
    'BloodGlucose': 'mg/dL'
  }
  return unitMap[measurementType] || ''
}

const formatDate = (date: string | undefined) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

const formatDateTime = (date: string | undefined) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}
</script>
