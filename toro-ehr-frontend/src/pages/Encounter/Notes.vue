<template>
  <ResizableBox title="Notes" type="notes">
    <div class="flex h-full">
      <!-- Left Column: Notes list -->
      <div class="w-1/5 overflow-y-auto border-r">
        <div
          v-for="note in notes"
          :key="note.id"
          @click="selectNote(note)"
          :class="[
            'p-2 cursor-pointer border-b hover:bg-gray-100',
            note.noteId === selectedNote?.noteId ? 'bg-gray-200' : '',
          ]"
        >
          <div class="flex justify-between text-sm font-medium text-gray-800">
            <span>{{ new Date(note.createdAt ?? '').toLocaleDateString() }}</span>
            <span>{{ note.practitionerName }}</span>
          </div>
          <div class="flex justify-between text-xs text-blue-600">
            <span>{{ note.name }}</span>
            <span class="text-red-600">{{ note.classification }}</span>
          </div>
          <div v-if="note.fields?.[0]?.value" class="text-xs text-gray-600 mt-1">
            {{ note.fields[0].value.slice(0, 140) }}
          </div>
        </div>
      </div>

      <!-- Middle Column: Selected note display -->
      <div v-if="selectedNote" class="w-2/6 p-4 overflow-y-auto border-r">
        <div class="flex items-center justify-between mb-2">
          <Tag :value="`Version: ${selectedNote.version}`" severity="info" rounded />
          <Button
            icon="pi pi-clock"
            @click="openVersionMenu"
            ref="versionButtonRef"
            text
            label="View other versions"
            class="text-xs"
          />
          <Button
            icon="pi pi-times"
            @click="selectedNote = null"
            text
            class="text-xs"
            aria-label="Close selected note"
          />
          <Menu ref="versionMenuRef" :model="versionMenuItems" :popup="true" />
        </div>
        <div>
          <div v-for="field in selectedNote.fields" :key="field.name" class="mb-4">
            <div class="flex items-center justify-between mb-1">
              <p class="text-sm font-semibold text-gray-700">
                {{ field.name }}
                <span v-if="field.isRequired" v-tooltip.top="'Required'" class="text-red-500">*</span>
              </p>
              <div class="flex gap-1">
                <!-- Web Speech API (offline, raw transcription only) -->
                <VoiceRecordButton
                  :section-name="`Selected Note - ${selectedNote.name}`"
                  :field-name="field.name"
                  @transcription-update="(text, isFinal) => handleTranscriptionUpdate(field, text, isFinal)"
                  @transcription-complete="(text) => handleTranscriptionComplete(field, text)"
                  @recording-error="handleRecordingError"
                />
                <!-- Whisper Small model -->
                <VoiceRecordButtonWhisper
                  model="small"
                  :section-name="`Selected Note - ${selectedNote.name}`"
                  :field-name="field.name"
                  @transcription-complete="(text) => handleTranscriptionComplete(field, text)"
                  @recording-error="handleRecordingError"
                />
                <!-- Whisper Turbo model -->
                <VoiceRecordButtonWhisper
                  model="turbo"
                  :section-name="`Selected Note - ${selectedNote.name}`"
                  :field-name="field.name"
                  @transcription-complete="(text) => handleTranscriptionComplete(field, text)"
                  @recording-error="handleRecordingError"
                />
                <!-- Whisper Distil model -->
                <VoiceRecordButtonWhisper
                  model="distil"
                  :section-name="`Selected Note - ${selectedNote.name}`"
                  :field-name="field.name"
                  @transcription-complete="(text) => handleTranscriptionComplete(field, text)"
                  @recording-error="handleRecordingError"
                />
                <!-- AI text formatting -->
                <TextFormatButton
                  :text="field.value?.toString() || ''"
                  :section-name="`Selected Note - ${selectedNote.name}`"
                  :field-name="field.name"
                  @text-formatted="(formattedText) => handleTextFormatted(field, formattedText)"
                  @format-error="handleRecordingError"
                />
              </div>
            </div>
            <!--            <div class="border p-2 bg-gray-100 text-sm whitespace-pre-wrap">{{ field.value }}</div>-->
            <Textarea
              :id="field.name!"
              label=""
              v-model="field.value"
              class="w-full"
              autoResize
              rows="5"
            />
          </div>
        </div>

        <!-- Medical Codes Section for Amend - Moved to top -->
        <div v-if="selectedNote" class="mt-6 space-y-4">
          <div class="flex items-center justify-between border-b border-gray-200 pb-2">
            <h3 class="text-lg font-semibold text-gray-800">Medical Codes</h3>
            <div class="flex gap-2">
              <!-- Copy to Assessment button -->
              <Button
                v-if="hasAssessmentFieldAmend && selectedAmendIcdCodes.length > 0"
                icon="pi pi-copy"
                label="Copy to Assessment"
                size="small"
                outlined
                @click="copyIcdCodesToAssessmentAmend"
                :disabled="isCopyingCodesAmend"
                :loading="isCopyingCodesAmend"
              />
              <!-- Voice recording buttons -->
              <div class="flex gap-1">
                <VoiceRecordButton
                  :section-name="`Amend Note - ${selectedNote.name}`"
                  field-name="Medical Codes"
                  @recording-error="handleRecordingError"
                />
                <VoiceRecordButtonAudio
                  :section-name="`Amend Note - ${selectedNote.name}`"
                  field-name="Medical Codes"
                  @recording-error="handleRecordingError"
                />
              </div>
            </div>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- ICD-10 Code Dropdown -->
            <div>
              <div class="my-3">
                <FloatLabel variant="on">
                  <MultiSelect
                    v-model="selectedAmendIcdCodes"
                    :options="amendIcdCodeOptions"
                    @filter="searchAmendIcdCodes"
                    @show="loadCommonAmendIcdCodes"
                    optionLabel="displayName"
                    input-id="selectedAmendIcdCodes"
                    fluid
                    filter
                    :maxSelectedLabels="2"
                    placeholder="Select ICD-10 codes"
                  />
                  <label for="selectedAmendIcdCodes">ICD-10 codes</label>
                </FloatLabel>
              </div>
            </div>

            <!-- CPT Code Dropdown -->
            <div>
              <div class="my-3">
                <FloatLabel variant="on">
                  <AutoComplete
                    v-model="selectedAmendCptCode"
                    :suggestions="amendCptCodeOptions"
                    @complete="searchAmendCptCodes"
                    @dropdown="loadCommonCptCodes"
                    optionLabel="clinicianDescriptor"
                    input-id="selectedAmendCptCode"
                    fluid
                    dropdown
                  />
                  <label for="selectedAmendCptCode">Time spent</label>
                </FloatLabel>
              </div>
            </div>
          </div>

          <!-- Selected ICD Codes Table -->
          <div v-if="selectedAmendIcdCodes.length > 0" class="mt-4">
            <h4 class="text-md font-medium text-gray-700 mb-2">Selected ICD-10 Codes</h4>
            <div class="border border-gray-200 rounded-lg overflow-hidden">
              <table class="w-full">
                <thead class="bg-gray-50">
                  <tr>
                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Code</th>
                    <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Description</th>
                    <th class="px-4 py-2 text-center text-sm font-medium text-gray-700">Actions</th>
                  </tr>
                </thead>
                <tbody class="divide-y divide-gray-200">
                  <tr v-for="(code, index) in selectedAmendIcdCodes" :key="code.code" class="hover:bg-gray-50">
                    <td class="px-4 py-2 text-sm font-mono text-gray-900">{{ code.code }}</td>
                    <td class="px-4 py-2 text-sm text-gray-900">{{ getIcdDescriptionAmend(code) }}</td>
                    <td class="px-4 py-2 text-center">
                      <Button
                        icon="pi pi-trash"
                        size="small"
                        text
                        severity="danger"
                        @click="removeAmendIcdCode(index)"
                        aria-label="Delete ICD code"
                      />
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>

        <!-- Current Medical Codes Display -->
        <div
          v-if="selectedNote && (selectedNote.icdCodes?.length || selectedNote.cptCode)"
          class="mt-4 p-3 bg-gray-50 rounded-lg"
        >
          <h4 class="text-sm font-semibold text-gray-700 mb-2">Current Medical Codes</h4>
          <div v-if="selectedNote.icdCodes?.length" class="mb-2">
            <span class="text-xs font-medium text-gray-600">ICD-10 Codes:</span>
            <div class="flex flex-wrap gap-1 mt-1">
              <Tag
                v-for="code in selectedNote.icdCodes"
                :key="code"
                :value="code"
                severity="info"
                class="text-xs"
              />
            </div>
          </div>
          <div v-if="selectedNote.cptCode">
            <span class="text-xs font-medium text-gray-600">CPT Code:</span>
            <Tag :value="selectedNote.cptCode" severity="success" class="text-xs ml-1" />
          </div>
        </div>

        <Button
          v-if="selectedNote"
          label="Amend Note"
          @click="amendNote"
          severity="warn"
          class="mt-4 px-4 py-2 float-right"
        />
      </div>

      <!-- Right Column: New note from template -->
      <div :class="selectedNote ? 'w-3/6' : 'w-5/6'">
        <NewNoteForm v-if="!isPatientView" @note-signed="refreshNotes" />
      </div>
    </div>
  </ResizableBox>
</template>
<script setup lang="ts">
import ResizableBox from '../../components/resizable/ResizableBox.vue'
import { computed, onMounted, ref, watch } from 'vue'
import Textarea from '@/components/form-extensions/InputTextAreaFluent.vue'
import { useEncounterStore } from '@/stores/encounter.ts'
import { api } from '@/api'
import type {
  NoteFieldResponse,
  PatientNoteResponse,
  CodingResponse,
  CptCodeResponse,
} from '@/api/api-reference.ts'
import type { AutoCompleteCompleteEvent } from 'primevue/autocomplete'
import Button from 'primevue/button'
import Menu from 'primevue/menu'
import Tag from 'primevue/tag'
import AutoComplete from 'primevue/autocomplete'
import MultiSelect from 'primevue/multiselect'
import Select from 'primevue/select'
import FloatLabel from 'primevue/floatlabel'
import * as yup from 'yup'
import { useForm } from 'vee-validate'
import NewNoteForm from '@/pages/Encounter/NewNoteForm.vue'
import VoiceRecordButton from '@/components/VoiceRecordButton.vue'
import VoiceRecordButtonAudio from '@/components/VoiceRecordButtonAudio.vue'
import VoiceRecordButtonWhisper from '@/components/VoiceRecordButtonWhisper.vue'
import TextFormatButton from '@/components/TextFormatButton.vue'
import debounce from 'lodash.debounce'
import { useRoute } from 'vue-router'

const encounterStore = useEncounterStore()
const notes = ref<PatientNoteResponse[]>([])

onMounted(async () => {
  if (encounterStore.selectedEncounter?.patientId) {
    const notesResult = await api.notes.noteListPatientNotes({
      patientId: encounterStore.selectedEncounter?.patientId,
    })
    notes.value = notesResult.data ?? []
  }

  // Initialize ICD codes with some common options for amend
  await loadCommonAmendIcdCodes()
})

watch(
  () => encounterStore.selectedEncounter?.patientId,
  async (newSelectedPatientId) => {
    const notesResult = await api.notes.noteListPatientNotes({ patientId: newSelectedPatientId })
    notes.value = notesResult.data ?? []
  },
)

const route = useRoute()
const isPatientView = computed(() => route.name === 'patient')

const selectedNote = ref<PatientNoteResponse | null>(null)

// Medical codes for amend note
const selectedAmendIcdCodes = ref<CodingResponse[]>([])
const selectedAmendCptCode = ref<CptCodeResponse | null>(null)
const amendIcdCodeOptions = ref<CodingResponse[]>([])
const amendCptCodeOptions = ref<CptCodeResponse[]>([])
const amendIcdLoading = ref(false)
const amendCptLoading = ref(false)
const isCopyingCodesAmend = ref(false)

// Check if template has an Assessment field for amend
const hasAssessmentFieldAmend = computed(() => {
  return selectedNote.value?.fields?.some(field =>
    field.name?.toLowerCase().includes('assessment')
  ) ?? false;
});

// Get ICD description for table display in amend
const getIcdDescriptionAmend = (code: CodingResponse) => {
  if (code.displayName && code.displayName.includes(' - ')) {
    return code.displayName.split(' - ').slice(1).join(' - ');
  }
  return code.displayName || code.code || '';
};

// Remove ICD code from selected list in amend
const removeAmendIcdCode = (index: number) => {
  selectedAmendIcdCodes.value.splice(index, 1);
  // Update options to reflect current selection
  amendIcdCodeOptions.value = [...selectedAmendIcdCodes.value];
};

// Copy selected ICD codes to Assessment field in amend
const copyIcdCodesToAssessmentAmend = async () => {
  if (!selectedNote.value?.fields || selectedAmendIcdCodes.value.length === 0) return;

  isCopyingCodesAmend.value = true;
  try {
    // Find Assessment field
    const assessmentField = selectedNote.value.fields.find(field =>
      field.name?.toLowerCase().includes('assessment')
    );

    if (!assessmentField) {
      console.log('No Assessment field found');
      return;
    }

    // Format ICD codes for insertion
    const icdCodesText = selectedAmendIcdCodes.value
      .map(code => `${code.code} - ${getIcdDescriptionAmend(code)}`)
      .join('\n');

    // Get existing assessment text
    const existingText = assessmentField.value?.toString().trim() || '';

    // Append ICD codes to assessment text
    if (existingText) {
      // Add ICD codes at the end with proper spacing
      const separator = existingText.endsWith('.') || existingText.endsWith(':') ? '\n\n' : '.\n\n';
      assessmentField.value = existingText + separator + 'ICD-10 Codes:\n' + icdCodesText;
    } else {
      // If assessment is empty, just add the ICD codes
      assessmentField.value = 'ICD-10 Codes:\n' + icdCodesText;
    }

    console.log(`Copied ${selectedAmendIcdCodes.value.length} ICD codes to Assessment field`);

  } catch (error) {
    console.error('Error copying ICD codes to Assessment:', error);
  } finally {
    isCopyingCodesAmend.value = false;
  }
};

const versionMenuItems = ref<Array<{ label: string; command: () => void }>>([])
const versionMenuRef = ref()
const versionButtonRef = ref()

const openVersionMenu = async (event: MouseEvent) => {
  versionMenuRef.value.toggle(event)
}

function buildValidationSchema(fields: NoteFieldResponse[], isNew: boolean) {
  const shape: Record<string, yup.AnySchema> = {}

  for (const field of fields) {
    let schema: yup.AnySchema = yup.string()
    if (field.isRequired) {
      schema = schema.required('This field is required')
    } else {
      schema = schema.nullable()
    }

    if (isNew) {
      shape[`new-${field.name!}`] = schema
    } else {
      shape[field.name!] = schema
    }
  }

  return yup.object().shape(shape)
}

const validationSchemaAmend = computed(() => {
  const fields = selectedNote.value?.fields ?? []
  return buildValidationSchema(fields, false)
})

const { handleSubmit: handleSubmitAmend } = useForm({
  validationSchema: validationSchemaAmend,
})

async function reloadVersions() {
  const versionData = (await api.notes.noteListPatientNotesVersions(selectedNote.value?.noteId!))
    .data
  versionMenuItems.value = versionData.map((v) => ({
    label: `v${v.version} – ${new Date(v.createdAt!).toLocaleString()}`,
    command: () => {
      selectedNote.value = v as any
    },
  }))
  return versionData
}

async function refreshNotes() {
  const notesResult = await api.notes.noteListPatientNotes({
    patientId: encounterStore.selectedEncounter?.patientId,
  })
  notes.value = notesResult.data ?? []
}

const amendNote = handleSubmitAmend(async (values) => {
  if (!selectedNote.value) return

  try {
    await api.notes.noteAmendNote({
      id: selectedNote.value.noteId!,
      patientId: encounterStore.selectedEncounter?.patientId,
      encounterId: encounterStore.selectedEncounter?.id,
      fields: Object.entries(values)
        .filter(([key]) => key !== 'template')
        .map(([key, value]) => ({
          name: key,
          value: value,
        })),
      icdCodes:
        (selectedAmendIcdCodes.value
          ?.map((code) => code.code)
          .filter((code) => code !== undefined) as string[]) ?? [],
      cptCode: selectedAmendCptCode.value?.code,
    })

    // Reset medical codes
    selectedAmendIcdCodes.value = []
    selectedAmendCptCode.value = null

    // Refresh notes list
    await refreshNotes()

    // Select newest version
    const versionData = await reloadVersions()
    selectedNote.value = versionData[0] ?? null
  } catch (error) {
    console.error('Failed to amend note', error)
  }
})

async function selectNote(note: PatientNoteResponse) {
  selectedNote.value = note
  await reloadVersions()

  // Populate medical codes from the selected note
  if (note.icdCodes && note.icdCodes.length > 0) {
    // Create display objects for the selected ICD codes
    const icdCodePromises = note.icdCodes.map(async (icdCode) => {
      const baseCode = {
        id: '',
        code: icdCode,
        displayName: icdCode, // Will be updated if we find full details
        codeSystem: '',
        codeSystemName: '',
        codeSystemVersion: '',
      }

      // Try to fetch full details from API
      try {
        const response = await api.icd10.icd10ListIcd10({
          searchParam: icdCode,
          pageNumber: 1,
          pageSize: 5,
        })
        const foundCode = response.data.items?.find((item) => item.code === icdCode)
        if (foundCode) {
          return {
            id: foundCode.id,
            code: foundCode.code,
            displayName: `${foundCode.code} - ${foundCode.displayName}`,
            codeSystem: foundCode.codeSystem,
            codeSystemName: foundCode.codeSystemName,
            codeSystemVersion: foundCode.codeSystemVersion,
          }
        }
      } catch (error) {
        console.error('Error fetching ICD code details:', error)
      }
      return baseCode
    })

    selectedAmendIcdCodes.value = await Promise.all(icdCodePromises)
    // Also populate the options with the selected codes so they show up in the dropdown
    amendIcdCodeOptions.value = [...selectedAmendIcdCodes.value]
  } else {
    selectedAmendIcdCodes.value = []
    amendIcdCodeOptions.value = []
  }

  if (note.cptCode) {
    // Check if it's one of the common codes first
    const existingCptCode = commonCptCodes.find((code) => code.code === note.cptCode)

    if (existingCptCode) {
      selectedAmendCptCode.value = existingCptCode
    } else {
      // Create a display object for the selected CPT code
      selectedAmendCptCode.value = {
        id: '',
        code: note.cptCode,
        conceptId: '',
        clinicianDescriptor: note.cptCode, // Will be updated if we find full details
      }

      // Try to fetch full details from API
      try {
        const response = await api.cptcodes.cptCodesListCptCodes({
          searchParam: note.cptCode,
          pageNumber: 1,
          pageSize: 5,
        })
        const foundCode = response.data.items?.find((item) => item.code === note.cptCode)
        if (foundCode) {
          selectedAmendCptCode.value = {
            id: foundCode.id,
            code: foundCode.code,
            conceptId: foundCode.conceptId,
            clinicianDescriptor: `${foundCode.code} - ${foundCode.clinicianDescriptor}`,
          }
        }
      } catch (error) {
        console.error('Error fetching CPT code details:', error)
      }
    }
  } else {
    selectedAmendCptCode.value = null
  }
}

// Most commonly used CPT codes
const commonCptCodes: CptCodeResponse[] = [
  {
    id: '1',
    code: '99213',
    conceptId: '',
    clinicianDescriptor: '99213 - Office visit, established patient, 15 min',
  },
  {
    id: '2',
    code: '99214',
    conceptId: '',
    clinicianDescriptor: '99214 - Office visit, established patient, 25 min',
  },
  {
    id: '3',
    code: '99215',
    conceptId: '',
    clinicianDescriptor: '99215 - Office visit, established patient, 40 min',
  },
  {
    id: '4',
    code: '99203',
    conceptId: '',
    clinicianDescriptor: '99203 - Office visit, new patient, 30 min',
  },
  {
    id: '5',
    code: '99204',
    conceptId: '',
    clinicianDescriptor: '99204 - Office visit, new patient, 45 min',
  },
  {
    id: '6',
    code: '99205',
    conceptId: '',
    clinicianDescriptor: '99205 - Office visit, new patient, 60 min',
  },
  {
    id: '7',
    code: '99212',
    conceptId: '',
    clinicianDescriptor: '99212 - Office visit, established patient, 10 min',
  },
  {
    id: '8',
    code: '99202',
    conceptId: '',
    clinicianDescriptor: '99202 - Office visit, new patient, 20 min',
  },
  {
    id: '9',
    code: '99211',
    conceptId: '',
    clinicianDescriptor: '99211 - Office visit, established patient, 5 min',
  },
  {
    id: '10',
    code: '99201',
    conceptId: '',
    clinicianDescriptor: '99201 - Office visit, new patient, 10 min',
  },
]

// Load common CPT codes when dropdown is clicked
const loadCommonCptCodes = () => {
  amendCptCodeOptions.value = [...commonCptCodes]
}

// Load common ICD codes when dropdown is opened for amend
const loadCommonAmendIcdCodes = async () => {
  if (amendIcdCodeOptions.value.length === 0) {
    // Load some common ICD codes for initial display
    try {
      const response = await api.icd10.icd10ListIcd10({
        searchParam: '',
        pageNumber: 1,
        pageSize: 20,
      })

      const commonCodes = response.data.items?.map((x) => ({
        id: x.id,
        code: x.code,
        displayName: `${x.code} - ${x.displayName}`,
        codeSystem: x.codeSystem,
        codeSystemName: x.codeSystemName,
        codeSystemVersion: x.codeSystemVersion,
      })) ?? []

      // Merge with currently selected codes
      const allCodes = [...selectedAmendIcdCodes.value, ...commonCodes]
      const uniqueCodes = allCodes.filter((code, index, self) =>
        index === self.findIndex(c => c.code === code.code)
      )

      amendIcdCodeOptions.value = uniqueCodes
    } catch (error) {
      console.error('Error loading common ICD codes for amend:', error)
      amendIcdCodeOptions.value = [...selectedAmendIcdCodes.value]
    }
  }
}

// ICD-10 Code Search for Amend
const searchAmendIcdCodes = debounce(async (event: any) => {
  await fetchAmendIcdCodes(event.value)
}, 300)

const fetchAmendIcdCodes = async (query: string) => {
  if (!query.trim() || query.length < 2) {
    // When no query, show currently selected codes plus any loaded common codes
    if (amendIcdCodeOptions.value.length === 0) {
      await loadCommonAmendIcdCodes()
    } else {
      // Ensure selected codes are always visible
      const allCodes = [...selectedAmendIcdCodes.value, ...amendIcdCodeOptions.value]
      const uniqueCodes = allCodes.filter((code, index, self) =>
        index === self.findIndex(c => c.code === code.code)
      )
      amendIcdCodeOptions.value = uniqueCodes
    }
    return
  }

  amendIcdLoading.value = true
  try {
    const response = await api.icd10.icd10ListIcd10({
      searchParam: query,
      pageNumber: 1,
      pageSize: 20,
    })

    const searchResults =
      response.data.items?.map((x) => ({
        id: x.id,
        code: x.code,
        displayName: `${x.code} - ${x.displayName}`,
        codeSystem: x.codeSystem,
        codeSystemName: x.codeSystemName,
        codeSystemVersion: x.codeSystemVersion,
      })) ?? []

    // Merge currently selected codes with search results, removing duplicates
    const allCodes = [...selectedAmendIcdCodes.value, ...searchResults]
    const uniqueCodes = allCodes.filter(
      (code, index, self) => index === self.findIndex((c) => c.code === code.code),
    )

    amendIcdCodeOptions.value = uniqueCodes
  } catch (error) {
    console.error('Error fetching ICD codes for amend:', error)
    amendIcdCodeOptions.value = [...selectedAmendIcdCodes.value]
  } finally {
    amendIcdLoading.value = false
  }
}

// CPT Code Search for Amend
const searchAmendCptCodes = debounce(async (event: AutoCompleteCompleteEvent) => {
  await fetchAmendCptCodes(event.query)
}, 300)

const fetchAmendCptCodes = async (query: string) => {
  if (!query.trim() || query.length < 2) {
    // If no query, show common codes
    amendCptCodeOptions.value = [...commonCptCodes]
    return
  }

  amendCptLoading.value = true
  try {
    const response = await api.cptcodes.cptCodesListCptCodes({
      searchParam: query,
      pageNumber: 1,
      pageSize: 20,
    })

    const searchResults =
      response.data.items?.map((x) => ({
        id: x.id,
        code: x.code,
        conceptId: x.conceptId,
        clinicianDescriptor: `${x.code} - ${x.clinicianDescriptor}`,
      })) ?? []

    // Combine common codes with search results, removing duplicates
    const allCodes = [...commonCptCodes, ...searchResults]
    const uniqueCodes = allCodes.filter(
      (code, index, self) => index === self.findIndex((c) => c.code === code.code),
    )

    amendCptCodeOptions.value = uniqueCodes
  } catch (error) {
    console.error('Error fetching CPT codes for amend:', error)
    amendCptCodeOptions.value = [...commonCptCodes]
  } finally {
    amendCptLoading.value = false
  }
}

const handleRecordingError = (error: string) => {
  console.error('Voice recording error:', error)
  // TODO: Add user-friendly error notification when toast/notification system is available
}

const handleTranscriptionUpdate = (field: NoteFieldResponse, text: string, isFinal: boolean) => {
  // For interim results, we don't want to permanently modify the field
  // This is handled by the speech recognition component internally
  // We could add visual feedback here if needed
}

const handleTranscriptionComplete = (field: NoteFieldResponse, finalText: string) => {
  // Append the final transcription to the existing field content
  if (field.value && field.value.toString().trim()) {
    const existingText = field.value.toString().trim()
    // Add proper spacing and punctuation
    const separator = existingText.endsWith('.') || existingText.endsWith('!') || existingText.endsWith('?') ? ' ' : '. '
    field.value = existingText + separator + finalText
  } else {
    field.value = finalText
  }

  console.log(`Transcription completed for field "${field.name}":`, finalText)
}

const handleTextFormatted = (field: NoteFieldResponse, formattedText: string) => {
  // Replace the field content with the formatted text
  field.value = formattedText
  console.log(`Text formatted for field "${field.name}":`, formattedText)
}
</script>
