<template>
  <div class="bg-white border border-gray-200 rounded-xl shadow-sm max-h-80 overflow-auto">
    <TableHeader>
      <template #inputs>
        <div class="relative max-w-sm">
          <h2 class="text-xl font-bold">Care Team</h2>
        </div>
      </template>
      <template #buttons>
        <span
          class="text-primary underline cursor-pointer text-sm flex py-2"
          @click="$emit('switch-table')"
        >
          Plan <ArrowUpRightIcon class="size-3 m-1"
        /></span>
      </template>
    </TableHeader>
    <table class="min-w-full divide-y divide-gray-200">
      <thead class="bg-gray-50">
        <tr>
          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800"> color </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-end">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800"> Specialty </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-end">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800"> Name </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-end">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800"> contact </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-end">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800"> last seen </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-end">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800"> orders </span>
            </div>
          </th>
          <th scope="col" class="ps-6 py-3 text-end">
            <div class="flex items-center gap-x-2">
              <span class="text-xs font-semibold tracking-wide text-gray-800"> results </span>
            </div>
          </th>
        </tr>
      </thead>

      <tbody class="divide-y divide-gray-200">
        <tr
          v-for="(row, index) in encounterStore.patientCareTeams"
          :key="index"
          class="relative hover:bg-gray-100 cursor-pointer"
        >
          <td class="h-px w-40 whitespace-nowrap">
            <div class="px-6 py-3">
              <span
                class="inline-block w-4 h-4 rounded-full"
                :style="{ backgroundColor: '#' + row.color }"
              ></span>
            </div>
          </td>
          <td class="h-px w-40 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-800">{{ row.spetialty }}</span>
            </div>
          </td>
          <td class="h-px w-40 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-800">{{ row.fullName }}</span>
            </div>
          </td>
          <td class="h-px w-40 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-800">{{ row.phone }}</span>
            </div>
          </td>
          <td class="h-px w-40 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-800">{{ formatDate(row.lastSeen) }}</span>
            </div>
          </td>
          <td class="h-px max-w-52 overflow-hidden whitespace-nowrap text-ellipsis">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-800 truncate" v-tooltip="row.order">{{
                row.order
              }}</span>
            </div>
          </td>
          <td class="h-px w-40 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm text-gray-800">{{ row.orderResult }}</span>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <!-- End Table -->
  </div>
</template>
<script setup lang="ts">
import TableHeader from '@/components/table/TableHeader.vue'
import { ArrowUpRightIcon } from '@heroicons/vue/24/outline'
import { useEncounterStore } from '@/stores/encounter'
import { formatDate } from '@/utils/timeMethods'

defineEmits(['switch-table'])

const encounterStore = useEncounterStore()
</script>
