<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-20"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
      <h2 class="text-lg font-bold mb-4">Add Problem</h2>

      <!-- Form -->
      <form @submit.prevent="addProblem">
        <FloatLabel variant="on" class="w-full">
          <AutoComplete
            v-model="selectedCondition"
            :suggestions="conditions"
            @complete="searchConditions"
            optionLabel="displayName"
            label="Condition"
            input-id="condition"
            fluid
            class="w-full"
          />
          <label for="condition">Condition</label>
        </FloatLabel>
        <Select
          id="clinicalConditionStatus"
          label="Clinical Condition Status"
          :options="clinicalConditionStatuses"
        />
        <Select
          id="conditionVerificationStatus"
          label="Condition Verification Status"
          :options="conditionVerificationStatuses"
        />
        <DateTimePicker id="diagnosisDate" label="Diagnosis date" dateFormat="m/d/yy" />
        <DateTimePicker id="abatement" label="Abatement" dateFormat="m/d/yy" />
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button @click="addProblem" class="bg-primary text-white px-4 py-2 rounded">Save</button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { api } from '@/api'
import Select from '@/components/form-extensions/SelectFluent.vue'
import DateTimePicker from '@/components/form-extensions/DateTimePickerFluent.vue'
import type { CodingResponse } from '@/api/api-reference'
import * as yup from 'yup'
import { ref } from 'vue'
import FloatLabel from 'primevue/floatlabel'
import AutoComplete, { type AutoCompleteCompleteEvent } from 'primevue/autocomplete'
import debounce from 'lodash.debounce'
import {
  getClinicalConditionStatuses,
  getConditionVerificationStatuses,
} from '@/utils/clinicalConditionStatuses'
import { useEncounterStore } from '@/stores/encounter'

const encounterStore = useEncounterStore()

const emit = defineEmits(['close'])
defineProps<{
  isModalOpen: boolean
}>()

const conditions = ref<CodingResponse[]>([])
const selectedCondition = ref()
const clinicalConditionStatuses = getClinicalConditionStatuses()
const conditionVerificationStatuses = getConditionVerificationStatuses()

const fetchConditions = async (query: string) => {
  if (!query.trim()) {
    return
  }
  const conditionsResponse = await api.icd10.icd10ListIcd10({ searchParam: query })
  conditions.value = conditionsResponse.data.items ?? []
}

const searchConditions = debounce((event: AutoCompleteCompleteEvent) => {
  fetchConditions(event.query)
}, 300)

const { handleSubmit } = useForm({
  validationSchema: yup.object({
    clinicalConditionStatus: yup.string().required(),
    conditionVerificationStatus: yup.string().required(),
    diagnosisDate: yup.string().required(),
    abatement: yup.string().nullable(),
  }),
})

const addProblem = handleSubmit(async (values) => {
  console.log(values)
  try {
    await api.patients.patientCreatePatientProblem({
      ...values,
      condition: selectedCondition.value.id,
      patientId: encounterStore.selectedEncounter!.patientId!,
      encounterId: encounterStore.selectedEncounter!.id!,
      diagnosisDate: values.diagnosisDate.toISOString(),
      abatement: values.abatement?.toISOString(),
    })
    emit('close')
  } catch (error) {
    console.log(error)
  }
})
</script>
