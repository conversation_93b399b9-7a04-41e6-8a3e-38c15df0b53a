<template>
  <ResizableBox title="CARE" type="care">
    <div class="px-4 sm:px-2 mx-auto">
      <div class="flex flex-col lg:flex-row gap-4">
        <div class="w-full lg:w-1/2">
          <div class="-m-1.5 overflow-x-auto">
            <div class="p-1.5 min-w-full inline-block align-middle">
              <div
                class="bg-white border border-gray-200 rounded-xl shadow-sm max-h-80 overflow-auto"
              >
                <TableHeader>
                  <template #inputs>
                    <div class="relative max-w-sm">
                      <h2 class="text-xl font-bold">Problems List</h2>
                    </div>
                  </template>

                  <template #buttons>
                    <a
                      class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
                      href="#"
                      @click.prevent="isAddModalOpen = true"
                    >
                      <PlusIcon class="shrink-0 w-4 h-4" />
                      New
                    </a>
                  </template>
                </TableHeader>
                <table class="min-w-full divide-y divide-gray-200">
                  <thead class="bg-gray-50">
                    <tr>
                      <th scope="col" class="ps-6 py-3 text-start">
                        <div class="flex items-center gap-x-2">
                          <span class="text-xs font-semibold tracking-wide text-gray-800">
                            Condition description
                          </span>
                        </div>
                      </th>
                      <th scope="col" class="ps-6 py-3 text-end">
                        <div class="flex items-center gap-x-2">
                          <span class="text-xs font-semibold tracking-wide text-gray-800">
                            cli. status
                          </span>
                        </div>
                      </th>
                      <th scope="col" class="ps-6 py-3 text-end">
                        <div class="flex items-center gap-x-2">
                          <span class="text-xs font-semibold tracking-wide text-gray-800">
                            ver. status
                          </span>
                        </div>
                      </th>
                      <th scope="col" class="ps-6 py-3 text-end">
                        <div class="flex items-center gap-x-2">
                          <span class="text-xs font-semibold tracking-wide text-gray-800">
                            diagnosis date
                          </span>
                        </div>
                      </th>
                      <th scope="col" class="ps-6 py-3 text-end">
                        <div class="flex items-center gap-x-2">
                          <span class="text-xs font-semibold tracking-wide text-gray-800">
                            abatement
                          </span>
                        </div>
                      </th>
                    </tr>
                  </thead>

                  <tbody class="divide-y divide-gray-200">
                    <tr
                      v-for="row in encounterStore.patientProblems"
                      :key="row.id"
                      class="relative hover:bg-gray-100 cursor-pointer"
                    >
                      <td class="h-px max-w-xs overflow-hidden whitespace-nowrap text-ellipsis">
                        <div class="px-6 py-3">
                          <span
                            class="block text-sm text-gray-800 truncate"
                            v-tooltip="row.conditionDescription"
                            >{{ row.conditionDescription }}</span
                          >
                        </div>
                      </td>
                      <td class="h-px w-40 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800">{{ row.clinicalStatus }}</span>
                        </div>
                      </td>
                      <td class="h-px w-40 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800">{{
                            row.verificationStatus
                          }}</span>
                        </div>
                      </td>
                      <td class="h-px w-72 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800">{{ row.diagnosisDate }}</span>
                        </div>
                      </td>
                      <td class="h-px w-72 whitespace-nowrap">
                        <div class="px-6 py-3">
                          <span class="block text-sm text-gray-800">{{ row.abatement }}</span>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
                <!-- End Table -->
              </div>
            </div>
          </div>
        </div>
        <div class="w-full lg:w-1/2">
          <div class="-m-1.5 overflow-x-auto">
            <div class="p-1.5 min-w-full inline-block align-middle">
              <CareTeam v-if="isCareTeamOpen" @switch-table="isCareTeamOpen = false" />
              <CarePlan v-if="!isCareTeamOpen" @switch-table="isCareTeamOpen = true" />
            </div>
          </div>
        </div>
      </div>
      <Timeline />
      <CareEventDetails />
    </div>
  </ResizableBox>
  <AddProblem :isModalOpen="isAddModalOpen" @close="closeModal" />
</template>
<script setup lang="ts">
import ResizableBox from '../../../components/resizable/ResizableBox.vue'
import TableHeader from '@/components/table/TableHeader.vue'
import { PlusIcon } from '@heroicons/vue/24/outline'
import { ref } from 'vue'
import AddProblem from './AddProblem.vue'
import { useEncounterStore } from '../../../stores/encounter'
import CareTeam from './CareTeam.vue'
import CarePlan from './CarePlan.vue'
import Timeline from './TimeLineEvents.vue'
import CareEventDetails from './CareEventDetails.vue'

const encounterStore = useEncounterStore()

const isAddModalOpen = ref(false)
const isCareTeamOpen = ref(true)
const closeModal = () => {
  encounterStore.getPatientProblems(encounterStore.selectedEncounter!.patientId!)
  encounterStore.getCare(
    encounterStore.selectedEncounter!.patientId!,
    encounterStore.selectedEncounter!.id!,
  )
  isAddModalOpen.value = false
}
</script>
