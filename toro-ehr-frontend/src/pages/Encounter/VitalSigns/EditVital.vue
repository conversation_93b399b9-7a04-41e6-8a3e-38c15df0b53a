<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-20"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
      <h2 class="text-lg font-bold mb-4">{{ title }}</h2>

      <!-- Form -->
      <form>
        <div v-if="curentVitalSigns?.type != 'BloodPressure'" class="px-2">
          <InputNumber
            :id="curentVitalSigns!.type!"
            :label="getMeasurmentLabel(curentVitalSigns!.type!)"
            mode="decimal"
            :minFractionDigits="0"
            :maxFractionDigits="5"
          />
        </div>
        <div v-if="curentVitalSigns?.type == 'BloodPressure'" class="px-2">
          <label class="block text-sm font-medium text-gray-700 mb-3" for="BloodPressure"
            >Blood Pressure</label
          >
          <InputMask
            id="BloodPressure"
            label=""
            :initialSystolic="systolic"
            :initialDiastolic="diastolic"
          />
        </div>
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button @click="recordVitals()" class="bg-primary text-white px-4 py-2 rounded mr-2">
            Save
          </button>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { api } from '@/api'
import { useEncounterStore } from '@/stores/encounter'
import InputNumber from '@/components/form-extensions/InputNumberFluent.vue'
import type { VitalSignRequest, VitalSignResponse } from '@/api/api-reference'
import { computed, onMounted, ref, watch } from 'vue'
import type { Measurement } from '@/utils/interfaces'
import InputMask from '@/components/form-extensions/InputMaskFluent.vue'

const encounterStore = useEncounterStore()

const emit = defineEmits(['close'])
const props = defineProps<{
  isModalOpen: boolean
  curentVitalSigns: VitalSignResponse | null
  status: string
  measurements: Measurement[]
}>()

const systolic = ref('')
const diastolic = ref('')

const title = computed(() =>
  props.status == 'amended'
    ? 'Amend Vital Sign'
    : props.status == 'corrected'
      ? 'Correct Vital Sign'
      : '',
)

const getMeasurmentLabel = (type: string) => {
  const measurement = props.measurements.filter((x) => x.type == type)[0]
  return measurement.unit ? `${measurement.textLong} (${measurement.unit})` : measurement.textLong
}

const { handleSubmit, setFieldValue } = useForm()

const recordVitals = handleSubmit(async (values) => {
  try {
    const vitals: VitalSignRequest[] = Object.entries(values).flatMap(([key, value]) => {
      if (key === 'BloodPressure' && typeof value === 'string') {
        const [systolic, diastolic] = value.split('/').map((v) => v.trim())
        const [systolicId, diastolicId] = props
          .curentVitalSigns!.id!.split('/')
          .map((v) => v.trim())

        return [
          {
            id: systolicId || undefined,
            type: 'SystolicBloodPressure',
            value: systolic || undefined,
          },
          {
            id: diastolicId || undefined,
            type: 'DiastolicBloodPressure',
            value: diastolic || undefined,
          },
        ].filter((v) => v.value !== undefined)
      }

      return value ? [{ id: props.curentVitalSigns?.id, type: key, value: String(value) }] : []
    })

    await api.encounter.encounterAddVitalSigns({
      vitals: vitals,
      patientId: encounterStore.selectedEncounter!.patientId!,
      encounterId: encounterStore.selectedEncounter?.id,
      status: props.status,
    })

    await encounterStore.getPatientVitalSigns(encounterStore.selectedEncounter!.patientId!)
    emit('close')
  } catch (error) {
    console.log(error)
  }
})

onMounted(() => {
  console.log(props.curentVitalSigns)

  if (props.curentVitalSigns) {
    if (props.curentVitalSigns?.type == 'BloodPressure') {
      const [sys, dia] = props.curentVitalSigns!.value!.split('/').map((v) => v.trim())
      systolic.value = sys
      diastolic.value = dia
    } else {
      setFieldValue(props.curentVitalSigns!.type!, props.curentVitalSigns?.value)
    }
    setFieldValue(props.curentVitalSigns.type!, props.curentVitalSigns.value)
  }
})

watch(
  () => props.curentVitalSigns,
  (newValue) => {
    if (newValue) {
      if (newValue?.type == 'BloodPressure') {
        const [sys, dia] = newValue!.value!.split('/').map((v) => v.trim())
        systolic.value = sys
        diastolic.value = dia
      } else {
        setFieldValue(newValue!.type!, newValue?.value)
      }
    }
  },
)
</script>
