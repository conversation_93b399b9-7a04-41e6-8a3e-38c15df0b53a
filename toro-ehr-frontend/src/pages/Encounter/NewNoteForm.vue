<template>
  <div class="p-4">
    <SelectFluent
      id="template"
      v-model="selectedTemplateId"
      :options="templates"
      label="New Note From Template"
      optionLabel="label"
      optionValue="value"
      class="w-full"
    />

    <!-- Medical Codes Section - At the very top -->
    <div v-if="selectedTemplate?.fields" class="mt-6 space-y-4">
      <div class="flex items-center justify-between border-b border-gray-200 pb-2">
        <h3 class="text-lg font-semibold text-gray-800">Medical Codes</h3>
        <div class="flex gap-2">
          <!-- Copy to Assessment button -->
          <Button
            v-if="hasAssessmentField && selectedIcdCodes.length > 0"
            icon="pi pi-copy"
            label="Copy to Assessment"
            size="small"
            outlined
            @click="copyIcdCodesToAssessment"
            :disabled="isCopyingCodes"
            :loading="isCopyingCodes"
          />
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- ICD-10 Code Dropdown -->
        <div>
          <div class="my-3">
            <FloatLabel variant="on">
              <MultiSelect
                v-model="selectedIcdCodes"
                :options="icdCodeOptions"
                @filter="searchIcdCodes"
                @show="loadCommonIcdCodes"
                optionLabel="displayName"
                input-id="selectedIcdCodes"
                fluid
                filter
                :maxSelectedLabels="2"
              />
              <label for="selectedIcdCodes">ICD-10 codes</label>
            </FloatLabel>
          </div>
        </div>

        <!-- CPT Code Dropdown -->
        <div>
          <div class="my-3">
            <FloatLabel variant="on">
              <AutoComplete
                v-model="selectedCptCode"
                :suggestions="cptCodeOptions"
                @complete="searchCptCodes"
                @dropdown="loadCommonCptCodes"
                optionLabel="clinicianDescriptor"
                input-id="selectedCptCode"
                fluid
                dropdown
              />
              <label for="selectedCptCode">Time spent</label>
            </FloatLabel>
          </div>
        </div>
      </div>

      <!-- Selected ICD Codes Table -->
      <div v-if="selectedIcdCodes.length > 0" class="mt-4">
        <h4 class="text-md font-medium text-gray-700 mb-2">Selected ICD-10 Codes</h4>
        <div class="border border-gray-200 rounded-lg overflow-hidden">
          <table class="w-full">
            <thead class="bg-gray-50">
              <tr>
                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Code</th>
                <th class="px-4 py-2 text-left text-sm font-medium text-gray-700">Description</th>
                <th class="px-4 py-2 text-center text-sm font-medium text-gray-700">Actions</th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr v-for="(code, index) in selectedIcdCodes" :key="code.code" class="hover:bg-gray-50">
                <td class="px-4 py-2 text-sm font-mono text-gray-900">{{ code.code }}</td>
                <td class="px-4 py-2 text-sm text-gray-900">{{ getIcdDescription(code) }}</td>
                <td class="px-4 py-2 text-center">
                  <Button
                    icon="pi pi-trash"
                    size="small"
                    text
                    severity="danger"
                    @click="removeIcdCode(index)"
                    aria-label="Delete ICD code"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <div v-if="selectedTemplate?.fields" class="mt-4 space-y-4">
      <div v-for="field in selectedTemplate.fields" :key="field.name">
        <div class="flex items-center justify-between mb-1">
          <p class="text-sm font-semibold text-gray-700">
            {{ field.name }}
            <span v-if="field.isRequired" v-tooltip.top="'Required'" class="text-red-500">*</span>
          </p>
          <div class="flex gap-1">
            <!-- Web Speech API (offline, raw transcription only) -->
            <VoiceRecordButton
              :section-name="`New Note - ${selectedTemplate.name}`"
              :field-name="field.name"
              @transcription-update="(text, isFinal) => handleTranscriptionUpdate(field, text, isFinal)"
              @transcription-complete="(text) => handleTranscriptionComplete(field, text)"
              @recording-error="handleRecordingError"
            />
            <!-- Whisper Small model -->
            <VoiceRecordButtonWhisper
              model="small"
              :section-name="`New Note - ${selectedTemplate.name}`"
              :field-name="field.name"
              @transcription-complete="(text) => handleTranscriptionComplete(field, text)"
              @recording-error="handleRecordingError"
            />
            <!-- Whisper Turbo model -->
            <VoiceRecordButtonWhisper
              model="turbo"
              :section-name="`New Note - ${selectedTemplate.name}`"
              :field-name="field.name"
              @transcription-complete="(text) => handleTranscriptionComplete(field, text)"
              @recording-error="handleRecordingError"
            />
            <!-- Whisper Distil model -->
            <VoiceRecordButtonWhisper
              model="distil"
              :section-name="`New Note - ${selectedTemplate.name}`"
              :field-name="field.name"
              @transcription-complete="(text) => handleTranscriptionComplete(field, text)"
              @recording-error="handleRecordingError"
            />
            <!-- AI text formatting -->
            <TextFormatButton
              :text="field.value?.toString() || ''"
              :section-name="`New Note - ${selectedTemplate.name}`"
              :field-name="field.name"
              @text-formatted="(formattedText) => handleTextFormatted(field, formattedText)"
              @format-error="handleRecordingError"
            />
          </div>
        </div>
        <Textarea :id="field.name!" label="" v-model="field.value" class="w-full" autoResize rows="5"/>
      </div>
    </div>

    <Button
      severity="success"
      label="Sign Note"
      @click="signNote"
      class="mt-4 px-4 py-2 float-right"
    />
  </div>
</template>

<script setup lang="ts">

import Textarea from "@/components/form-extensions/InputTextAreaFluent.vue";
import SelectFluent from "@/components/form-extensions/SelectFluent.vue";
import VoiceRecordButton from "@/components/VoiceRecordButton.vue";
import VoiceRecordButtonAudio from "@/components/VoiceRecordButtonAudio.vue";
import VoiceRecordButtonWhisper from "@/components/VoiceRecordButtonWhisper.vue";
import TextFormatButton from "@/components/TextFormatButton.vue";
import Button from "primevue/button";
import AutoComplete from 'primevue/autocomplete'
import MultiSelect from 'primevue/multiselect'
import Select from 'primevue/select'
import FloatLabel from 'primevue/floatlabel'
import {api} from "@/api";
import {computed, onMounted, ref, watch} from "vue";
import {useForm} from "vee-validate";
import type {NoteFieldResponse, NoteTemplateDetailsResponse, CodingResponse, CptCodeResponse} from "@/api/api-reference.ts";
import type {AutoCompleteCompleteEvent} from 'primevue/autocomplete'
import * as yup from "yup";
import {useEncounterStore} from "@/stores/encounter.ts";
import debounce from 'lodash.debounce';

const encounterStore = useEncounterStore();

const selectedTemplateId = ref('');
const selectedTemplate = ref<NoteTemplateDetailsResponse>();
const templates = ref<Array<{label: string, value: string}>>([]);

// Medical codes
const selectedIcdCodes = ref<CodingResponse[]>([]);
const selectedCptCode = ref<CptCodeResponse | null>(null);
const icdCodeOptions = ref<CodingResponse[]>([]);
const cptCodeOptions = ref<CptCodeResponse[]>([]);
const icdLoading = ref(false);
const cptLoading = ref(false);
const isCopyingCodes = ref(false);

const emit = defineEmits(['note-signed']);

// Check if template has an Assessment field
const hasAssessmentField = computed(() => {
  return selectedTemplate.value?.fields?.some(field =>
    field.name?.toLowerCase().includes('assessment')
  ) ?? false;
});

// Get ICD description for table display
const getIcdDescription = (code: CodingResponse) => {
  if (code.displayName && code.displayName.includes(' - ')) {
    return code.displayName.split(' - ').slice(1).join(' - ');
  }
  return code.displayName || code.code || '';
};

// Remove ICD code from selected list
const removeIcdCode = (index: number) => {
  selectedIcdCodes.value.splice(index, 1);
  // Update options to reflect current selection
  icdCodeOptions.value = [...selectedIcdCodes.value];
};

// Copy selected ICD codes to Assessment field
const copyIcdCodesToAssessment = async () => {
  if (!selectedTemplate.value?.fields || selectedIcdCodes.value.length === 0) return;

  isCopyingCodes.value = true;
  try {
    // Find Assessment field
    const assessmentField = selectedTemplate.value.fields.find(field =>
      field.name?.toLowerCase().includes('assessment')
    );

    if (!assessmentField) {
      console.log('No Assessment field found');
      return;
    }

    // Format ICD codes for insertion
    const icdCodesText = selectedIcdCodes.value
      .map(code => `${code.code} - ${getIcdDescription(code)}`)
      .join('\n');

    // Get existing assessment text
    const existingText = assessmentField.value?.toString().trim() || '';

    // Append ICD codes to assessment text
    if (existingText) {
      // Add ICD codes at the end with proper spacing
      const separator = existingText.endsWith('.') || existingText.endsWith(':') ? '\n\n' : '.\n\n';
      assessmentField.value = existingText + separator + 'ICD-10 Codes:\n' + icdCodesText;
    } else {
      // If assessment is empty, just add the ICD codes
      assessmentField.value = 'ICD-10 Codes:\n' + icdCodesText;
    }

    console.log(`Copied ${selectedIcdCodes.value.length} ICD codes to Assessment field`);

  } catch (error) {
    console.error('Error copying ICD codes to Assessment:', error);
  } finally {
    isCopyingCodes.value = false;
  }
};

function buildValidationSchema(fields: NoteFieldResponse[]) {
  const shape: Record<string, yup.AnySchema> = {}

  for (const field of fields) {
    let schema: yup.AnySchema = yup.string()
    if (field.isRequired) {
      schema = schema.required('This field is required')
    } else {
      schema = schema.nullable()
    }

    shape[field.name!] = schema
  }

  return yup.object().shape(shape)
}


const validationSchemaNew = computed(() => {
  const fields = selectedTemplate.value?.fields ?? []
  return buildValidationSchema(fields)
})

const {handleSubmit: handleSubmitNew} = useForm({
  validationSchema: validationSchemaNew
})

const signNote = handleSubmitNew(async (values) => {
  if (!selectedTemplate.value) return
  try {
    await api.notes.noteCreateNote({
      name: selectedTemplate.value.name,
      patientId: encounterStore.selectedEncounter?.patientId,
      classification: selectedTemplate.value.classification,
      encounterId: encounterStore.selectedEncounter?.id,
      fields: Object.entries(values)
        .filter(([key]) => key !== 'template')
        .map(([key, value]) => ({
          name: key,
          value: value,
        })),
      icdCodes: selectedIcdCodes.value?.map(code => code.code).filter(code => code !== undefined) as string[] ?? [],
      cptCode: selectedCptCode.value?.code,
    })

    // Reset form
    selectedTemplateId.value = ''
    selectedTemplate.value = undefined
    selectedIcdCodes.value = []
    selectedCptCode.value = null

    emit('note-signed')

  } catch (error) {
    console.error('Failed to create note', error)
  }
});

// Most commonly used CPT codes
const commonCptCodes: CptCodeResponse[] = [
  { id: '1', code: '99213', conceptId: '', clinicianDescriptor: '99213 - Office visit, established patient, 15 min' },
  { id: '2', code: '99214', conceptId: '', clinicianDescriptor: '99214 - Office visit, established patient, 25 min' },
  { id: '3', code: '99215', conceptId: '', clinicianDescriptor: '99215 - Office visit, established patient, 40 min' },
  { id: '4', code: '99203', conceptId: '', clinicianDescriptor: '99203 - Office visit, new patient, 30 min' },
  { id: '5', code: '99204', conceptId: '', clinicianDescriptor: '99204 - Office visit, new patient, 45 min' },
  { id: '6', code: '99205', conceptId: '', clinicianDescriptor: '99205 - Office visit, new patient, 60 min' },
  { id: '7', code: '99212', conceptId: '', clinicianDescriptor: '99212 - Office visit, established patient, 10 min' },
  { id: '8', code: '99202', conceptId: '', clinicianDescriptor: '99202 - Office visit, new patient, 20 min' },
  { id: '9', code: '99211', conceptId: '', clinicianDescriptor: '99211 - Office visit, established patient, 5 min' },
  { id: '10', code: '99201', conceptId: '', clinicianDescriptor: '99201 - Office visit, new patient, 10 min' }
]

// Load common CPT codes when dropdown is clicked
const loadCommonCptCodes = () => {
  cptCodeOptions.value = [...commonCptCodes]
}

// Load common ICD codes when dropdown is opened
const loadCommonIcdCodes = async () => {
  if (icdCodeOptions.value.length === 0) {
    // Load some common ICD codes for initial display
    try {
      const response = await api.icd10.icd10ListIcd10({
        searchParam: '',
        pageNumber: 1,
        pageSize: 20,
      })

      const commonCodes = response.data.items?.map((x) => ({
        id: x.id,
        code: x.code,
        displayName: `${x.code} - ${x.displayName}`,
        codeSystem: x.codeSystem,
        codeSystemName: x.codeSystemName,
        codeSystemVersion: x.codeSystemVersion,
      })) ?? []

      // Merge with currently selected codes
      const allCodes = [...selectedIcdCodes.value, ...commonCodes]
      const uniqueCodes = allCodes.filter((code, index, self) =>
        index === self.findIndex(c => c.code === code.code)
      )

      icdCodeOptions.value = uniqueCodes
    } catch (error) {
      console.error('Error loading common ICD codes:', error)
      icdCodeOptions.value = [...selectedIcdCodes.value]
    }
  }
}

// ICD-10 Code Search
const searchIcdCodes = debounce(async (event: any) => {
  await fetchIcdCodes(event.value)
}, 300)

const fetchIcdCodes = async (query: string) => {
  if (!query.trim() || query.length < 2) {
    // When no query, show currently selected codes plus any loaded common codes
    if (icdCodeOptions.value.length === 0) {
      await loadCommonIcdCodes()
    } else {
      // Ensure selected codes are always visible
      const allCodes = [...selectedIcdCodes.value, ...icdCodeOptions.value]
      const uniqueCodes = allCodes.filter((code, index, self) =>
        index === self.findIndex(c => c.code === code.code)
      )
      icdCodeOptions.value = uniqueCodes
    }
    return
  }

  icdLoading.value = true
  try {
    const response = await api.icd10.icd10ListIcd10({
      searchParam: query,
      pageNumber: 1,
      pageSize: 20,
    })

    const searchResults = response.data.items?.map((x) => ({
      id: x.id,
      code: x.code,
      displayName: `${x.code} - ${x.displayName}`,
      codeSystem: x.codeSystem,
      codeSystemName: x.codeSystemName,
      codeSystemVersion: x.codeSystemVersion,
    })) ?? []

    // Merge currently selected codes with search results, removing duplicates
    const allCodes = [...selectedIcdCodes.value, ...searchResults]
    const uniqueCodes = allCodes.filter((code, index, self) =>
      index === self.findIndex(c => c.code === code.code)
    )

    icdCodeOptions.value = uniqueCodes
  } catch (error) {
    console.error('Error fetching ICD codes:', error)
    icdCodeOptions.value = [...selectedIcdCodes.value]
  } finally {
    icdLoading.value = false
  }
}

// CPT Code Search
const searchCptCodes = debounce(async (event: AutoCompleteCompleteEvent) => {
  await fetchCptCodes(event.query)
}, 300)

const fetchCptCodes = async (query: string) => {
  if (!query.trim() || query.length < 2) {
    // If no query, show common codes
    cptCodeOptions.value = [...commonCptCodes]
    return
  }

  cptLoading.value = true
  try {
    const response = await api.cptcodes.cptCodesListCptCodes({
      searchParam: query,
      pageNumber: 1,
      pageSize: 20,
    })

    const searchResults = response.data.items?.map((x) => ({
      id: x.id,
      code: x.code,
      conceptId: x.conceptId,
      clinicianDescriptor: `${x.code} - ${x.clinicianDescriptor}`,
    })) ?? []

    // Combine common codes with search results, removing duplicates
    const allCodes = [...commonCptCodes, ...searchResults]
    const uniqueCodes = allCodes.filter((code, index, self) =>
      index === self.findIndex(c => c.code === code.code)
    )

    cptCodeOptions.value = uniqueCodes
  } catch (error) {
    console.error('Error fetching CPT codes:', error)
    cptCodeOptions.value = [...commonCptCodes]
  } finally {
    cptLoading.value = false
  }
}

onMounted(async () => {
  // Load note templates
  const result = await api.noteTemplates.noteTemplateListNoteTemplates({
    pageNumber: 1,
    pageSize: 100,
    searchParam: "",
    specialtyFilter: ""
  });
  templates.value = result.data.items?.map((x) => ({
    label: `${x.name}`,
    value: `${x.id}`,
  })) ?? []

  // Initialize ICD codes with some common options
  await loadCommonIcdCodes()
});

watch(
  () => selectedTemplateId.value,
  async (newSelectedTemplateId) => {
    selectedTemplate.value = (await api.noteTemplates.noteTemplateGetNoteTemplate(newSelectedTemplateId)).data;
  },
)

const handleRecordingError = (error: string) => {
  console.error('Voice recording error:', error)
  // TODO: Add user-friendly error notification when toast/notification system is available
}

const handleTranscriptionUpdate = (field: NoteFieldResponse, text: string, isFinal: boolean) => {
  // For interim results, we don't want to permanently modify the field
  // This is handled by the speech recognition component internally
  // We could add visual feedback here if needed
}

const handleTranscriptionComplete = (field: NoteFieldResponse, finalText: string) => {
  // Append the final transcription to the existing field content
  if (field.value && field.value.toString().trim()) {
    const existingText = field.value.toString().trim()
    // Add proper spacing and punctuation
    const separator = existingText.endsWith('.') || existingText.endsWith('!') || existingText.endsWith('?') ? ' ' : '. '
    field.value = existingText + separator + finalText
  } else {
    field.value = finalText
  }

  console.log(`Transcription completed for field "${field.name}":`, finalText)
}

const handleTextFormatted = (field: NoteFieldResponse, formattedText: string) => {
  // Replace the field content with the formatted text
  field.value = formattedText
  console.log(`Text formatted for field "${field.name}":`, formattedText)
}
</script>
