<template>
  <!-- Two-column layout: Address & Contact Info | Communication & Emergency Contacts -->
  <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">

    <!-- Left Column: Address & Basic Contact Info -->
    <div class="space-y-6">
      <!-- Address Section -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">Address</h3>
        <div class="space-y-4">
          <InputText id="streetAddress" v-model="address.street" label="Street address" class="w-full"/>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <InputText id="city" label="City" v-model="address.city" class="w-full"/>
            <Select id="state" label="State" :options="states" v-model="address.state" class="w-full"/>
            <InputText id="zip" label="Zip" class="w-full" v-model="address.zipCode"/>
          </div>
        </div>
      </div>

      <!-- Previous Address Section -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">Previous Address</h3>
        <div class="space-y-4">
          <InputText id="previousStreetAddress" v-model="previousAddress.street" label="Street address"/>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <InputText id="previousCity" label="City" v-model="previousAddress.city"/>
            <Select id="previousState" label="State" :options="states" v-model="previousAddress.state"/>
            <InputText id="previousZip" label="Zip" v-model="previousAddress.zipCode"/>
          </div>
        </div>
      </div>

      <!-- Additional Info Section -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">Additional Information</h3>
        <div class="space-y-4">
          <InputText id="socialSecurityNumber" label="Social Security Number" v-model="socialSecurityNumber" />
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Select id="preferredContactMethod" label="Preferred contact method" :options="contactMethods" v-model="preferredContactMethod"/>
            <InputText id="preferredContactName" label="Preferred contact name" v-model="preferredContactName"/>
          </div>
        </div>
      </div>
    </div>
    <!-- Right Column: Communication & Emergency Contacts -->
    <div class="space-y-6">
      <!-- Emails Section -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">Email Addresses</h3>
        <div class="space-y-3">
          <div
            v-for="(emailAddress, index) in emails"
            :key="'email-' + index"
            class="flex items-center gap-3 p-3 border rounded-lg bg-gray-50"
          >
            <InputText id="email" v-model="emailAddress.email" label="Email" class="flex-1"/>
            <div class="flex items-center gap-2 whitespace-nowrap">
              <Checkbox v-model="emailAddress.isPrimary" :binary="true" :disabled="hasPrimary && !emailAddress.isPrimary"
                        input-id="primaryEmail"/>
              <label for="primaryEmail" class="text-gray-700 text-sm">Primary</label>
            </div>
            <i
              v-if="emails.length > 1"
              class="pi pi-trash text-red-500 cursor-pointer text-lg hover:text-red-700"
              @click="removeEmail(index)"
            ></i>
          </div>
        </div>
        <div class="flex justify-end mt-4">
          <Button icon="pi pi-plus-circle" label="Add email" variant="text" :disabled="!isLastEmailFilled"
                  class="text-blue-600 flex items-center gap-1 font-medium" @click="addEmail"/>
        </div>
      </div>
      <!-- Phone Numbers Section -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">Phone Numbers</h3>
        <div class="space-y-3">
          <div
            v-for="(phoneObj, index) in phoneNumbers"
            :key="'phone-' + index"
            class="flex items-center gap-3 p-3 border rounded-lg bg-gray-50"
          >
            <div class="grid grid-cols-1 md:grid-cols-2 gap-3 flex-1">
              <InputText id="phone" v-model="phoneObj.number" label="Phone number"/>
              <Select id="phoneType" v-model="phoneObj.type" :options="phoneTypes" label="Phone type"/>
            </div>
            <div class="flex items-center gap-2 whitespace-nowrap">
              <Checkbox v-model="phoneObj.isPrimary" :binary="true" :disabled="hasPrimaryPhone && !phoneObj.isPrimary"
                        input-id="primaryPhoneNumber"/>
              <label for="primaryPhoneNumber" class="text-gray-700 text-sm">Primary</label>
            </div>
            <i
              v-if="phoneNumbers.length > 1"
              class="pi pi-trash text-red-500 cursor-pointer text-lg hover:text-red-700"
              @click="removePhoneNumber(index)"
            ></i>
          </div>
        </div>
        <div class="flex justify-end mt-4">
          <Button icon="pi pi-plus-circle" label="Add phone" variant="text" :disabled="!isLastPhoneFilled"
                  class="text-blue-600 flex items-center gap-1 font-medium" @click="addPhoneNumber"/>
        </div>
      </div>
      <!-- Emergency Contacts Section -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h3 class="text-lg font-semibold mb-4 text-gray-800">Emergency Contacts</h3>
        <div class="space-y-3">
          <div
            v-for="(contact, index) in contacts"
            :key="'contact-' + index"
            class="p-3 border rounded-lg bg-gray-50"
          >
            <div class="grid grid-cols-1 md:grid-cols-3 gap-3 mb-3">
              <InputText id="contact.name" v-model="contact.name" label="Name"/>
              <InputText id="contact.phoneNumber" v-model="contact.phoneNumber" label="Phone number"/>
              <Select id="contactRelation" v-model="contact.relationship" label="Relation" :options="relations"/>
            </div>
            <div class="flex items-center justify-between">
              <div class="flex items-center gap-2">
                <Checkbox v-model="contact.primary" :binary="true" :disabled="hasPrimaryContact && !contact.primary"
                          input-id="primaryContact"/>
                <label for="primaryContact" class="text-gray-700 text-sm">Primary</label>
              </div>
              <i
                v-if="contacts.length > 1"
                class="pi pi-trash text-red-500 cursor-pointer text-lg hover:text-red-700"
                @click="removeContact(index)"
              ></i>
            </div>
          </div>
        </div>
        <div class="flex justify-end mt-4">
          <Button icon="pi pi-plus-circle" label="Add contact" variant="text"
                  class="text-blue-600 flex items-center gap-1 font-medium"
                  :disabled="!isLastContactFilled"
                  @click="addContact"/>
        </div>
      </div>
    </div>
  </div>

  <!-- Sticky Footer with Save Button -->
  <div class="sticky bottom-0 bg-white border-t border-gray-200 p-4 mt-8 z-10">
    <div class="flex justify-end">
      <Button label="Save" @click="save"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import {computed, ref, watchEffect} from 'vue';
import {getAllStates} from "@/utils/states.ts";
import Select from '../../components/form-extensions/SelectFluent.vue'
import InputText from "@/components/form-extensions/InputTextFluent.vue";
import {getContactMethods} from "@/utils/contactMethods.ts";
import Button from "primevue/button";
import Checkbox from 'primevue/checkbox';
import Divider from "primevue/divider";
import type {
  AddressRequest,
  EmailAddressRequest,
  EmergencyContactRequest,
  PhoneNumberRequest
} from "@/api/api-reference.ts";
import {api} from "@/api";
import {usePatientStore} from "@/stores/patient.ts";
import {useToast} from "vue-toastification";

const states = getAllStates()
const contactMethods = getContactMethods()
const relations = [
  {text: "Child", value: "Child"},
  {text: "Mother", value: "Mother"},
  {text: "Father", value: "Father"},
  {text: "Sibling", value: "Sibling"},
  {text: "Spouse", value: "Spouse"},
  {text: "Great grandparents", value: "Great grandparents"},
  {text: "Extended family member", value: "Extended family member"},
  {text: "Friend", value: "Friend"},
  {text: "Neighbor", value: "Neighbor"}
];

/*EMAILS*/
const emails = ref<EmailAddressRequest[]>([{
  email: '',
  isPrimary: false
}]);

const hasPrimary = computed(() => emails.value.some(e => e.isPrimary));
const isLastEmailFilled = computed(() => {
  const lastEmail = emails.value[emails.value.length - 1];
  return lastEmail && lastEmail.email?.trim() !== "";
});
const addEmail = () => {
  emails.value.push({email: '', isPrimary: false});
};
const removeEmail = (index: number) => {
  emails.value.splice(index, 1);
};

/*PHONES*/
const phoneTypes = [
  {text: "Mobile", value: "Mobile"},
  {text: "Home", value: "Home"},
  {text: "Work", value: "Work"},
];

const phoneNumbers = ref<PhoneNumberRequest[]>([{
  number: '',
  type: '',
  isPrimary: false
}]);

const hasPrimaryPhone = computed(() => phoneNumbers.value.some(p => p.isPrimary));
const isLastPhoneFilled = computed(() => {
  const lastPhone = phoneNumbers.value[phoneNumbers.value.length - 1];
  return lastPhone && lastPhone.number?.trim() !== "";
});

const addPhoneNumber = () => {
  phoneNumbers.value.push({number: '', type: '', isPrimary: false});
};

const removePhoneNumber = (index: number) => {
  if (phoneNumbers.value.length > 1) {
    phoneNumbers.value.splice(index, 1);
  }
};

/*EMERGENCY CONTACTS*/
const contacts = ref<EmergencyContactRequest[]>([
  {name: '', phoneNumber: '', relationship: '', primary: false}
]);

const hasPrimaryContact = computed(() => contacts.value.some(c => c.primary));

const isLastContactFilled = computed(() => {
  const lastContact = contacts.value[contacts.value.length - 1];
  return lastContact && lastContact.name?.trim() !== "" && lastContact.phoneNumber?.trim() !== "" && lastContact.relationship?.trim() !== "";
});

const addContact = () => {
  contacts.value.push({name: '', phoneNumber: '', relationship: '', primary: false});
};

const removeContact = (index: number) => {
  if (contacts.value.length > 1) {
    contacts.value.splice(index, 1);
  }
};

const patientStore = usePatientStore();
const toast = useToast();

const address = ref<AddressRequest>({
  street: '',
  city: '',
  state: '',
  zipCode: '',
});

const previousAddress = ref<AddressRequest>({
  street: '',
  city: '',
  state: '',
  zipCode: '',
});
const socialSecurityNumber = ref('');
const preferredContactMethod = ref('');
const preferredContactName = ref('');

const fetchContactInfo = async () => {
  try {
    const profile = patientStore.patientProfile;
    if (!profile) return;

    emails.value = profile.emails && profile.emails.length > 0
      ? profile.emails.map(e => ({
        email: e.email || '',
        isPrimary: e.primary ?? false
      }))
      : [{ email: '', isPrimary: false }];

    phoneNumbers.value = profile.phones && profile.phones.length > 0
      ? profile.phones.map(p => ({
        number: p.number || '',
        type: p.type || '',
        isPrimary: p.primary ?? false
    }))
      : [{ number: '', type: '', isPrimary: false }];

    contacts.value = profile.emergencyContacts && profile.emergencyContacts.length > 0
      ? profile.emergencyContacts.map(c => ({
        name: c.name || '',
        phoneNumber: c.phoneNumber || '',
        relationship: c.relationship || '',
        primary: c.primary ?? false
      }))
      : [{ name: '', phoneNumber: '', relationship: '', primary: false }];

    address.value = profile.address || {};
    previousAddress.value = profile.previousAddress || {};
    socialSecurityNumber.value = profile.socialSecurityNumber || "";
    preferredContactMethod.value = profile.preferredContactMethod || "";
    preferredContactName.value = profile.preferredContactName || "";

  } catch (error) {
    console.error("Error fetching contact information:", error);
  }
};

const save = async () => {
  // check if previous address has any filled fields
  const hasPreviousAddress = previousAddress.value.street?.trim() ||
                            previousAddress.value.city?.trim() ||
                            previousAddress.value.state?.trim() ||
                            previousAddress.value.zipCode?.trim();

  const data = {
    address: address.value,
    previousAddress: hasPreviousAddress ? previousAddress.value : null,
    socialSecurityNumber: socialSecurityNumber.value,
    preferredContactMethod: preferredContactMethod.value,
    preferredContactName: preferredContactName.value,
    emails: emails.value,
    phoneNumbers: phoneNumbers.value,
    emergencyContacts: contacts.value
  };

  try {
    await api.patients.patientSetContactInformation(data);
    await patientStore.getPatientProfile();
    toast.success("Contact information updated successfully");
  } catch (error) {
    console.error("Error updating contact information:", error);
    toast.error("Failed to update contact information");
  }
};

watchEffect(() => {
  fetchContactInfo();
});

// Expose save method for parent component
defineExpose({
  save
});

</script>
