<template>
  <div class="organization-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      {{ 'Encounter on ' + formatDate(encounter?.encounterDate) }}
    </h2>

    <!-- Responsive flex container -->
    <div class="flex flex-col lg:flex-row lg:space-x-4">
      <!-- Messages section -->
      <div class="lg:w-1/2">
        <TableSection>
          <div v-for="message in messages" :key="message.id" class="border-b p-4">
            <div class="flex items-baseline justify-between mb-1">
              <div class="font-semibold text-gray-900">{{ message.senderName }} wrote:</div>
              <div class="text-xs text-gray-500">
                {{ formatDate(message.sentAt) }}
              </div>
            </div>

            <div class="text-gray-800 whitespace-pre-wrap pl-4 mb-2">
              {{ message.message }}
            </div>

            <div v-if="message?.attachments?.length" class="pl-4 space-y-1">
              <div v-for="(file, index) in message.attachments" :key="index">
                <Button
                  variant="link"
                  :label="getFileNameFromUrl(file)"
                  @click="openFileNewTab(file)"
                />
              </div>
            </div>
          </div>
        </TableSection>
      </div>

      <!-- Compose message section -->
      <div class="lg:w-1/2 mt-4 lg:mt-0">
        <div class="px-4">
          <InputTextFluent id="subject" label="Subject" />

          <div class="flex items-center space-x-2 mb-4">
            <FileUpload
              mode="basic"
              chooseLabel="Attachments"
              chooseIcon="pi pi-paperclip"
              custom-upload
              auto
              :multiple="true"
              @select="handleFileSelect($event)"
            />
          </div>

          <div v-if="selectedFiles.length" class="mb-4 space-y-1">
            <div
              v-for="file in selectedFiles"
              :key="file.name"
              class="flex items-center justify-between bg-gray-100 px-2 py-1 rounded"
            >
              <span>{{ file.name }}</span>
              <Button variant="link" icon="pi pi-trash" @click="removeAttachment(file.name)" />
            </div>
          </div>

          <Textarea id="message" label="Message" rows="10" cols="20" />

          <!-- Right-align the Send button -->
          <div class="flex justify-end">
            <Button
              label="Send"
              @click="sendEmail"
              class="font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getFileNameFromUrl } from '@/utils/stringUtils'
import Textarea from '@/components/form-extensions/TextareaFluent.vue'
import Button from 'primevue/button'
import { onMounted, ref } from 'vue'
import { api } from '@/api'
import { useRoute } from 'vue-router'
import { formatDate } from '@/utils/timeMethods'
import type {
  EncounterCommunicationMessageResponse,
  EncounterDetailsResponse,
} from '@/api/api-reference'
import TableSection from '@/components/table/TableSection.vue'
import FileUpload, { type FileUploadSelectEvent } from 'primevue/fileupload'
import { useForm } from 'vee-validate'
import InputTextFluent from '@/components/form-extensions/InputTextFluent.vue'

const route = useRoute()
const encounterId = route.params.id as string
const encounter = ref<EncounterDetailsResponse>()
const messages = ref<EncounterCommunicationMessageResponse[]>([])
const selectedFiles = ref<File[]>([])

function openFileNewTab(url: string | null) {
  if (url) window.open(url, '_blank')
}

onMounted(async () => {
  encounter.value = (await api.encounter.encounterGetEncounterById(encounterId)).data
  await fetchData()
})

const { handleSubmit, setValues, resetForm } = useForm()

const handleFileSelect = (event: FileUploadSelectEvent) => {
  selectedFiles.value = event.files
}

const removeAttachment = (name: string) => {
  selectedFiles.value = selectedFiles.value.filter((x: { name: string }) => x.name != name)
}

const sendEmail = handleSubmit(async (values) => {
  try {
    const data = {
      ...values,
      Attachments: selectedFiles.value,
      MessageType: 'Email',
      EncounterId: encounterId,
      SentByPatient: true,
    }
    await api.encounter.encounterSendEncounterMessage(encounterId, data)
    resetForm()
    selectedFiles.value = []
    await fetchData()
  } catch (error) {
    console.log(error)
  }
})

const fetchData = async () => {
  const response = await api.encounter.encounterGetCommunications(encounterId)
  messages.value = response?.data ?? []
  setValues({ subject: 'Encounter on ' + formatDate(encounter.value?.encounterDate) })

  const unreadMessages = messages.value.filter((x) => !x.sendByPatient && !x.seenByRecipient)
  unreadMessages.forEach(async (msg) => {
    await api.encounter.encounterMarkMessageAsSeen(encounterId, { MessageId: msg.id })
  })
}
</script>
