<template>
  <div class="organization-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      Patients
    </h2>
    <TableSection>
      <TableHeader>
        <template #inputs>
          <div class="relative max-w-xs">
            <label class="sr-only">Search</label>
            <input
              @input="filter()"
              type="text"
              class="py-2 px-3 ps-9 block w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
              placeholder="Search"
              v-model="search"
            />
            <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
              <MagnifyingGlassIcon class="size-4 text-gray-400" />
            </div>
          </div>
        </template>

        <template #buttons>
          <!-- <a
            class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
            href="#"
            @click.prevent="openEditModal(undefined)"
          >
            <PlusIcon class="shrink-0 w-4 h-4" />
            Add patient
          </a> -->
        </template>
      </TableHeader>
      <!-- Table -->
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Name
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Email
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Phone
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-end"></th>
          </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
          <tr v-for="row in itemList" :key="row.id">
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800"
                  >{{ row.firstName }} {{ row.lastName }}</span
                >
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{ row.email }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{ row.phoneNumber }}</span>
              </div>
            </td>

            <td class="size-px whitespace-nowrap">
              <div class="flex items-center">
                <div class="relative flex flex-col items-center group">
                  <a
                    class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                    href="#"
                    @click.prevent="router.push({ name: 'patient', params: { id: row.id! } })"
                  >
                    View
                  </a>
                  <!-- <a
                    class="rounded border border-transparent focus:outline-none focus:border-gray-800 focus:shadow-outline-gray"
                  >
                    <div
                      class="p-2 bg-gray-100 hover:bg-gray-200 rounded cursor-pointer text-blue-600"
                    >
                      <PencilSquareIcon class="w-4 h-4" />
                    </div>
                  </a>

                  <div
                    class="absolute bottom-0 flex flex-col items-center hidden mb-6 group-hover:flex"
                  >
                    <span
                      class="relative z-10 p-2 text-xs leading-none text-white whitespace-no-wrap rounded-md bg-gray-700 shadow-lg"
                    >
                      Edit
                    </span>
                    <div class="w-3 h-3 -mt-2 rotate-45 bg-gray-700 mb-4"></div>
                  </div>
                </div>
                <div class="relative flex flex-col items-center group">
                  <a
                    class="rounded border border-transparent focus:outline-none focus:border-gray-800 focus:shadow-outline-gray"
                  >
                    <div
                      class="p-2 bg-gray-100 hover:bg-gray-200 rounded cursor-pointer text-blue-600"
                    >
                      <CalendarIcon class="w-4 h-4" />
                    </div>
                  </a>

                  <div
                    class="absolute bottom-0 flex flex-col items-center hidden mb-6 group-hover:flex"
                  >
                    <span
                      class="relative z-10 p-2 text-xs leading-none text-white whitespace-no-wrap rounded-md bg-gray-700 shadow-lg"
                    >
                      Create appointment
                    </span>
                    <div class="w-3 h-3 -mt-2 rotate-45 bg-gray-700 mb-4"></div>
                  </div> -->
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End Table -->

      <TableFooter
        :totalItems="totalItems"
        :isFirstPage="isFirstPage"
        :isLastPage="isLastPage"
        @prevPage="prevPage"
        @nextPage="nextPage"
      />
    </TableSection>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { api } from '../../api'
import type { PatientResponse } from '../../api/api-reference'
import {
  MagnifyingGlassIcon,
  PlusIcon,
  CheckIcon,
  PencilSquareIcon,
  DocumentTextIcon,
  CalendarIcon,
} from '@heroicons/vue/24/outline'
import debounce from 'lodash.debounce'
import TableHeader from '../../components/table/TableHeader.vue'
import TableFooter from '../../components/table/TableFooter.vue'
import TableSection from '../../components/table/TableSection.vue'
import { useRouter } from 'vue-router'

const router = useRouter()

const limit = 10

const itemList = ref<PatientResponse[]>()
const pageNumber = ref(1)
const totalPages = ref(1)
const totalItems = ref(0)
const search = ref('')

const isModalOpen = ref(false)
const selectedRow = ref<PatientResponse | null>(null)

const openEditModal = (row: PatientResponse | undefined) => {
  selectedRow.value = row || null
  isModalOpen.value = true
}

onMounted(async () => {
  fetchData()
})

const isFirstPage = computed(() => pageNumber.value === 1)
const isLastPage = computed(() => pageNumber.value === totalPages.value)

const nextPage = () => {
  if (!isLastPage.value) {
    pageNumber.value++
    fetchData()
  }
}

const prevPage = () => {
  if (!isFirstPage.value) {
    pageNumber.value--
    fetchData()
  }
}

const fetchData = async () => {
  try {
    const result = await api.patients.patientListPatients({
      pageNumber: pageNumber.value,
      pageSize: limit,
      searchParam: search.value,
    })
    itemList.value = result.data.items
    pageNumber.value = result.data.pageNumber ?? 1
    totalPages.value = result.data.totalPages ?? 1
    totalItems.value = result.data.totalItems ?? 0
  } catch (error) {
    console.error('Error fetching organizations:', error)
  }
}

const closeModal = () => {
  fetchData()
  isModalOpen.value = false
}

const filter = debounce(() => {
  fetchData()
}, 500)
</script>
