<template>
  <div class="organization-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      Records
    </h2>
    <TableSection>
      <TableHeader>
        <template #inputs>
          <div class="relative max-w-xs">
            <label class="sr-only">Search</label>
            <input
              @input="filter()"
              type="text"
              class="py-2 px-3 ps-9 block w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
              placeholder="Search"
              v-model="search"
            />
            <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
              <i class="pi pi-search text-gray-400"></i>
            </div>
          </div>
        </template>
        <template #buttons>
          <Select
            v-model="filterSelected"
            :options="filterOptions"
            optionLabel="text"
            option-value="value"
            placeholder="Filter"
            class="w-full md:w-56"
            @change="filter"
          />
        </template>
      </TableHeader>
      <!-- Table -->
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Practitioner
                </span>
              </div>
            </th>
            <!--          <th scope="col" class="ps-6 py-3 text-start">
            <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Organization
                </span>
            </div>
          </th>-->
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Location
                </span>
              </div>
            </th>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Date
                </span>
              </div>
            </th>
            <th scope="col" class="px-6 py-3 text-end"></th>
          </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
          <tr v-for="row in itemList" :key="row.id">
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800">{{
                  row.practitionerName
                }}</span>
              </div>
            </td>

            <!--          <td class="h-px w-72 whitespace-nowrap">
            <div class="px-6 py-3">
              <span class="block text-sm font-semibold text-gray-500">{{ row.locationName }}</span>
            </div>
          </td>-->

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{ row.locationName }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500"> {{ formatDate(row.startAt) }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
              <div class="flex items-right gap-x-4">
                <a
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="router.push({ name: 'record-details', params: { id: row.id } })"
                >
                  View
                </a>
<!--                <a
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="exportRecord(row.id)"
                >
                  Export
                </a>-->
                <OverlayBadge v-if="row.hasUnseenMessages" severity="danger" value="!">
                  <a
                    class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                    href="#"
                    @click.prevent="router.push({ name: 'communication', params: { id: row.id } })"
                  >
                    Messages
                  </a>
                </OverlayBadge>
                <a
                  v-else
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="router.push({ name: 'communication', params: { id: row.id } })"
                >
                  Messages
                </a>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End Table -->

      <TableFooter
        :totalItems="totalItems"
        :isFirstPage="isFirstPage"
        :isLastPage="isLastPage"
        @prevPage="prevPage"
        @nextPage="nextPage"
      />
    </TableSection>
  </div>


</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { api } from '@/api'
import type { PatientRecordResponse } from '@/api/api-reference.ts'
import debounce from 'lodash.debounce'
import TableHeader from '../../components/table/TableHeader.vue'
import TableFooter from '../../components/table/TableFooter.vue'
import TableSection from '../../components/table/TableSection.vue'
import Select from 'primevue/select'
import OverlayBadge from 'primevue/overlaybadge'

const router = useRouter()
const limit = 10

const itemList = ref<PatientRecordResponse[]>([])
const pageNumber = ref(1)
const totalPages = ref(1)
const totalItems = ref(0)
const search = ref('')
const filterSelected = ref('')
const filterOptions = ref([
  { text: 'All Records', value: '' },
  { text: 'Recent', value: 'recent' },
  { text: 'This Month', value: 'month' }
])

const isFirstPage = computed(() => pageNumber.value === 1)
const isLastPage = computed(() => pageNumber.value === totalPages.value)

const nextPage = () => {
  if (!isLastPage.value) {
    pageNumber.value++
    fetchData()
  }
}

const prevPage = () => {
  if (!isFirstPage.value) {
    pageNumber.value--
    fetchData()
  }
}

const fetchData = async () => {
  try {
    const response = await api.patients.patientListRecords({
      pageNumber: pageNumber.value,
      pageSize: limit,
      searchParam: search.value,
    })
    const data = response.data
    itemList.value = data.items || []
    pageNumber.value = data.pageNumber || 1
    totalPages.value = data.totalPages || 1
    totalItems.value = data.totalItems || 0
  } catch (error) {
    console.error('Error fetching records:', error)
  }
}

const filter = debounce(() => {
  fetchData()
}, 500)

const formatDate = (date: string | undefined) => {
  if (!date) return ''
  return new Date(date).toLocaleDateString()
}

const exportRecord = (recordId: string | undefined) => {
  if (!recordId) return

  // Navigate to record details page where the user can use the Download PDF button
  router.push({ name: 'record-details', params: { id: recordId } })
}

onMounted(async () => {
  await fetchData()
})
</script>
