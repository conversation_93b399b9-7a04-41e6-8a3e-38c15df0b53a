<template>
  <div
    v-if="isModalOpen"
    class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-[99]"
  >
    <div class="bg-white p-6 rounded shadow-md w-full max-w-md max-h-[90vh] overflow-hidden">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-bold">New Patient</h2>
        <span
          class="text-primary underline cursor-pointer text-sm flex"
          @click="$emit('switch-form')"
        >
          Existing Patient <ArrowUpRightIcon class="size-3 m-1"
        /></span>
      </div>
      <div class="overflow-y-auto max-h-[70vh]">
        <!-- Form -->
        <form @submit.prevent="createNewPatientAppointment">
          <InputText id="firstName" label="First Name" />
          <InputText id="lastName" label="Last Name" />
          <InputText id="email" label="Email" />
          <InputText id="phoneNumber" label="Phone Number" />
          <DateTimePicker id="birthday" label="Birthday" dateFormat="m/d/yy" />
          <Select
            id="employeeId"
            label="Practitioner"
            :options="appointmentsStore.practitionerLookups"
          />
          <InputNumber id="durationInMinutes" label="Duration" suffix=" minutes" />
          <DateTimePicker
            id="startAt"
            label="Start"
            showTime
            hourFormat="12"
            dateFormat="m/d/yy"
            :stepMinute="5"
          />
        </form>
        <div class="flex justify-end">
          <button type="button" class="text-gray-500 px-4 py-2 mr-2" @click="$emit('close')">
            Cancel
          </button>
          <button
            @click="createNewPatientAppointment"
            class="bg-primary text-white px-4 py-2 rounded"
          >
            Save
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useForm } from 'vee-validate'
import { api } from '../../api'
import InputText from '../../components/form-extensions/InputTextFluent.vue'
import type { CreateNewPatientAppointmentCommand } from '../../api/api-reference'
import * as yup from 'yup'
import { useAppointmentsStore } from '@/stores/appointments.ts'
import InputNumber from '../../components/form-extensions/InputNumberFluent.vue'
import DateTimePicker from '../../components/form-extensions/DateTimePickerFluent.vue'
import Select from '../../components/form-extensions/SelectFluent.vue'
import { ArrowUpRightIcon } from '@heroicons/vue/24/outline'
import { ref, watch } from 'vue'
import { useAuthStore } from '@/stores/auth'
import moment from 'moment-timezone'

const authStore = useAuthStore()
const appointmentsStore = useAppointmentsStore()

const emit = defineEmits(['close', 'switch-form'])
const props = defineProps<{
  isModalOpen: boolean
  startDate: Date | null
}>()

const shift = ref(0)

const initialValues = {
  firstName: '',
  lastName: '',
  email: '',
  phoneNumber: '',
  durationInMinutes: 15,
  birthday: new Date('1/1/2000'),
  startAt: new Date(),
  locationId: authStore.user!.locationId!,
}

const schema = yup.object({
  firstName: yup.string().required('First Name is required'),
  lastName: yup.string().required('Last Name is required'),
  email: yup.string().email('Invalid email address').required('Email is required'),
  employeeId: yup.string().required('Practitioner is required'),
  phoneNumber: yup.string().required('Phone number is required'),
  birthday: yup.string().required('Birthday is required'),
  durationInMinutes: yup.number().required('Duration is required'),
  locationId: yup.string().required('Location is required'),
  startAt: yup.string().required('Start at is required'),
})

const { handleSubmit, resetForm, setFieldValue } = useForm({
  validationSchema: schema,
  initialValues: initialValues,
})

const createNewPatientAppointment = handleSubmit(async (values) => {
  try {
    const form: CreateNewPatientAppointmentCommand = {
      ...values,
      birthday: values.birthday.toISOString(),
      startAt: moment(values.startAt).subtract(shift.value, 'minutes').toISOString(),
    }
    await api.appointments.appointmentCreateForNewPatient(form)
    resetForm()
    emit('close')
  } catch (error) {
    console.log(error)
  }
})

watch(
  () => props.isModalOpen,
  async (newValue) => {
    if (newValue) {
      resetForm()
      if (props.startDate) {
        const timezoneMoment = moment.tz(props.startDate, authStore.user!.timeZone ?? 'local')
        const timezoneOffset = timezoneMoment.utcOffset()
        const localOffset = moment().utcOffset()
        shift.value = timezoneOffset - localOffset

        const adjusted = timezoneMoment.add(shift.value, 'minutes').toDate()
        setFieldValue('startAt', adjusted)
      }
      const employeResponse = await api.employees.employeeGetLocationEmployee(
        authStore.user!.employeeId!,
        authStore.user!.locationId!,
      )
      setFieldValue('durationInMinutes', employeResponse.data.appointmentDurationInMinutes ?? 15)
    }
  },
)
</script>
