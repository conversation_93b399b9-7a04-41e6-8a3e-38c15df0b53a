<template>
  <div class="organization-page">
    <h2 class="font-semibold text-4xl leading-10 text-grey-800 px-4 py-4 sm:px-6 lg:px-8 lg:py-4">
      Locations
    </h2>
    <TableSection>
      <TableHeader>
        <template #inputs>
          <div class="relative max-w-xs">
            <label class="sr-only">Search</label>
            <input
              @input="filter()"
              type="text"
              class="py-2 px-3 ps-9 block w-full border border-gray-200 shadow-sm rounded-lg text-sm focus:z-10 focus:border-blue-500 focus:ring-blue-500 disabled:opacity-50 disabled:pointer-events-none"
              placeholder="Search"
              v-model="search"
            />
            <div class="absolute inset-y-0 start-0 flex items-center pointer-events-none ps-3">
              <MagnifyingGlassIcon class="size-4 text-gray-400" />
            </div>
          </div>
        </template>

        <template #buttons>
          <a
            class="py-2 px-3 inline-flex items-center gap-x-2 text-sm font-medium rounded-lg border border-transparent bg-primary text-white hover:bg-blue-700 focus:outline-none focus:bg-blue-700 disabled:opacity-50 disabled:pointer-events-none"
            href="#"
            @click.prevent="openEditModal(undefined)"
          >
            <PlusIcon class="shrink-0 w-4 h-4" />
            Add location
          </a>
        </template>
      </TableHeader>
      <!-- Table -->
      <table class="min-w-full divide-y divide-gray-200">
        <thead class="bg-gray-50">
          <tr>
            <th scope="col" class="ps-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Default
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Name
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Classification
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  TIN
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Phone
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-start">
              <div class="flex items-center gap-x-2">
                <span class="text-xs font-semibold uppercase tracking-wide text-gray-800">
                  Address
                </span>
              </div>
            </th>

            <th scope="col" class="px-6 py-3 text-end"></th>
          </tr>
        </thead>

        <tbody class="divide-y divide-gray-200">
          <tr v-for="row in itemList" :key="row.id">
            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span v-if="row.isDefault" class="block text-sm font-semibold text-gray-800 ml-4"
                  ><CheckIcon class="size-4 text-gray-500"
                /></span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm font-semibold text-gray-800">{{ row.name }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{ row.classification }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{ row.taxIdentificationNumber }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">{{ row.phoneNumber }}</span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap">
              <div class="px-6 py-3">
                <span class="block text-sm text-gray-500">
                  <AddressFormatter
                    :street="row.address?.street"
                    :city="row.address?.city"
                    :state="row.address?.state"
                    :zipCode="row.address?.zipCode"
                /></span>
              </div>
            </td>

            <td class="h-px w-72 whitespace-nowrap px-6 py-1.5">
              <div class="flex items-right gap-x-4">
                <!-- Edit Button -->
                <a
                  class="inline-flex items-center gap-x-1 text-sm text-primary font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="openEditModal(row)"
                >
                  Edit
                </a>
                <!-- Deactivate Button -->
                <a
                  class="inline-flex items-center gap-x-1 text-sm text-red-500 font-medium hover:underline focus:outline-none focus:underline"
                  href="#"
                  @click.prevent="openDeactivateModal(row.id!)"
                >
                  Deactivate
                </a>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
      <!-- End Table -->

      <TableFooter
        :totalItems="totalItems"
        :isFirstPage="isFirstPage"
        :isLastPage="isLastPage"
        @prevPage="prevPage"
        @nextPage="nextPage"
      />
    </TableSection>

    <EditLocation :isModalOpen="isModalOpen" :location="selectedRow" @close="closeModal" />
    <div
      v-if="isDeactivateModalOpen"
      class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
    >
      <div class="bg-white p-6 rounded shadow-md w-full max-w-md">
        <h2 class="text-lg font-bold mb-4">Deactivate Location</h2>
        <p class="text-gray-700 mb-6">
          Are you sure you want to deactivate this location? This action cannot be undone.
        </p>
        <div class="flex justify-end space-x-4">
          <button
            @click="deactivate"
            class="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
          >
            Confirm
          </button>
          <button
            @click="closeDeactivateModal"
            class="bg-gray-300 text-gray-700 px-4 py-2 rounded hover:bg-gray-400"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { api } from '../../api'
import EditLocation from './EditLocation.vue'
import type { LocationResponse, DeactivateLocationCommand } from '../../api/api-reference'
import {
  MagnifyingGlassIcon,
  PlusIcon,
  CheckIcon,
  PencilSquareIcon,
  TrashIcon,
} from '@heroicons/vue/24/outline'
import debounce from 'lodash.debounce'
import TableHeader from '../../components/table/TableHeader.vue'
import TableFooter from '../../components/table/TableFooter.vue'
import TableSection from '../../components/table/TableSection.vue'
import AddressFormatter from '../../components/Address.vue'
import { useAuthStore } from '@/stores/auth'

const authStore = useAuthStore()

const limit = 10

const itemList = ref<LocationResponse[]>()
const pageNumber = ref(1)
const totalPages = ref(1)
const totalItems = ref(0)
const search = ref('')

const isModalOpen = ref(false)
const isDeactivateModalOpen = ref(false)
const selectedRow = ref<LocationResponse | null>(null)
const itemForDeactivation = ref<DeactivateLocationCommand>({})

onMounted(async () => {
  await fetchData()
})

const isFirstPage = computed(() => pageNumber.value === 1)
const isLastPage = computed(() => pageNumber.value === totalPages.value)

const nextPage = () => {
  if (!isLastPage.value) {
    pageNumber.value++
    fetchData()
  }
}

const prevPage = () => {
  if (!isFirstPage.value) {
    pageNumber.value--
    fetchData()
  }
}

const fetchData = async () => {
  try {
    const result = await api.locations.locationListLocations({
      pageNumber: pageNumber.value,
      pageSize: limit,
      searchParam: search.value,
    })
    itemList.value = result.data.items
    pageNumber.value = result.data.pageNumber ?? 1
    totalPages.value = result.data.totalPages ?? 1
    totalItems.value = result.data.totalItems ?? 0
  } catch (error) {
    console.error('Error fetching organizations:', error)
  }
}

const openEditModal = (location: LocationResponse | undefined) => {
  if (location) {
    selectedRow.value = location
  } else {
    selectedRow.value = null
  }
  isModalOpen.value = true
}

const openDeactivateModal = (id: string) => {
  itemForDeactivation.value = { id }
  isDeactivateModalOpen.value = true
}

const deactivate = async () => {
  await api.locations.locationDeactivate(itemForDeactivation.value)
  closeDeactivateModal()
}

const closeDeactivateModal = () => {
  fetchData()
  isDeactivateModalOpen.value = false
}

const closeModal = async () => {
  fetchData()
  await authStore.getUser()
  isModalOpen.value = false
}

const filter = debounce(() => {
  fetchData()
}, 500)
</script>
