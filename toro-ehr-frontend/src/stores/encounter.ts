import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import { api } from '@/api'
import type {
  CareEventsResponse,
  CarePlanResponse,
  CareTeamResponse,
  EncounterCommunicationMessageResponse,
  EncounterQuestionnaireResponse,
  EncounterResponse,
  ImagingResultResponse,
  LaboratoryResultResponse,
  OrderResponse,
  PatientActiveMedication,
  PatientImmunizationResponse,
  PatientProblemResponse,
  PatientProfileResponse,
  VitalSignResponse,
} from '@/api/api-reference'
import type { EncounterBox } from '@/utils/interfaces'
import { generateGuid } from '@/utils/stringUtils'
import type { useRoute, useRouter } from 'vue-router'

export const useEncounterStore = defineStore('encounter', () => {
  const activeEncounters = ref<EncounterResponse[]>([])
  const selectedEncounter = ref<EncounterResponse | undefined>()
  const patientProfile = ref<PatientProfileResponse>()
  const patientImmunizations = ref<PatientImmunizationResponse[]>()
  const laboratoryResults = ref<LaboratoryResultResponse[]>([])
  const imagingResults = ref<ImagingResultResponse[]>([])
  const encounterMessages = ref<EncounterCommunicationMessageResponse[]>([])
  const encounterOrders = ref<OrderResponse[]>([])
  const editingOrder = ref<OrderResponse | null>()
  const vitalSigns = ref<VitalSignResponse[]>([])
  const editingBundleId = ref<string | null>()
  const patientActiveMedicationOrders = ref<PatientActiveMedication[]>()
  const patientProblems = ref<PatientProblemResponse[]>()
  const patientCareTeams = ref<CareTeamResponse[]>()
  const patientCarePlans = ref<CarePlanResponse[]>()
  const patientCareEvents = ref<CareEventsResponse[]>()
  const questionnaires = ref<EncounterQuestionnaireResponse[]>()

  const parentContainerHeight = ref(window.innerHeight - 200)
  const parentContainerWidth = ref(window.innerWidth)
  const encounterBoxes = ref<EncounterBox[]>([])

  const isMobile = computed(() => parentContainerWidth.value < 768)
  const isTablet = computed(
    () => parentContainerWidth.value < 1280 && parentContainerWidth.value > 768,
  )

  async function getEncounter(encounter: EncounterResponse) {
    try {
      if (encounter) {
        selectedEncounter.value = encounter
        await getPatientVitalSigns(encounter.patientId!)
        // await getEncounterQuestionnaires(encounter.id!)
        await getLaboratoryResults(encounter.patientId!)
        await getImagingResults(encounter.patientId!)
        await getEncounterMessages(encounter.id!)
        await getEncounterOrders(encounter.id!)
        await getPatientProfile(encounter.patientId!)
        await getPatientImmunization(encounter.patientId!)
        await getPatientActiveMedications(encounter.patientId!)
        await getPatientProblems(encounter.patientId!)
        await getCare(encounter.patientId!, encounter.id!)
      }
    } catch (error) {
      console.error(error)
    }
  }

  function resetSelectedValues() {
    activeEncounters.value = []
    selectedEncounter.value = undefined
    patientProfile.value = undefined
    patientImmunizations.value = []
    laboratoryResults.value = []
    imagingResults.value = []
    encounterMessages.value = []
    encounterOrders.value = []
    editingOrder.value = undefined
    vitalSigns.value = []
    editingBundleId.value = undefined
    patientActiveMedicationOrders.value = []
    patientProblems.value = []
    patientCareTeams.value = []
    patientCarePlans.value = []
    patientCareEvents.value = []
    questionnaires.value = []
  }

  async function getActiveEncounters() {
    try {
      resetSelectedValues()
      // Fetch profile
      const response = await api.encounter.encounterListActiveEncounters()
      activeEncounters.value = response?.data ?? []
    } catch (error) {
      console.error(error)
    }
  }

  async function getLatestPatientEncounter(patientId: string) {
    try {
      // Fetch profile
      const response = await api.encounter.encounterGetLatestPatientEncounter(patientId)
      if (response?.data) {
        activeEncounters.value = [response.data]
        setSelectedEncounterById(response.data.id!)
      } else {
        resetSelectedValues()
      }
    } catch (error) {
      console.error(error)
    }
  }

  async function getPatientVitalSigns(patientId: string) {
    try {
      const response = await api.patients.patientListVitalSigns(patientId)
      vitalSigns.value = response?.data
    } catch (error) {
      console.error(error)
    }
  }

  async function getEncounterQuestionnaires(encounterId: string) {
    try {
      // Fetch profile
      const response = await api.encounter.encounterGetEncounterQuestionnaires(encounterId)
      questionnaires.value = response?.data
    } catch (error) {
      console.error(error)
    }
  }

  function setEditingOrder(order: OrderResponse | null, bundleId: string | null) {
    editingOrder.value = order
    editingBundleId.value = bundleId
  }

  function setSelectedEncounter(encounter: EncounterResponse) {
    selectedEncounter.value = encounter
  }

  function setSelectedEncounterById(encounterId: string) {
    selectedEncounter.value = activeEncounters.value.find(
      (encounter) => encounter.id === encounterId,
    )
  }

  function setSelectedEncounterDefault(
    router: ReturnType<typeof useRouter>,
    route: ReturnType<typeof useRoute>,
  ) {
    selectedEncounter.value = activeEncounters.value?.[0]
    router.replace({ name: route.name!, params: { id: selectedEncounter.value?.id } })
  }

  async function getLaboratoryResults(patientId: string) {
    try {
      // Fetch profile
      const response = await api.patients.patientGetLaboratory(patientId)
      laboratoryResults.value = response?.data ?? []
    } catch (error) {
      console.error(error)
    }
  }

  async function getImagingResults(patientId: string) {
    try {
      // Fetch profile
      const response = await api.patients.patientGetImagingResults(patientId)
      imagingResults.value = response?.data ?? []
    } catch (error) {
      console.error(error)
    }
  }

  async function getEncounterMessages(encounterId: string) {
    try {
      // Fetch profile
      const response = await api.encounter.encounterGetCommunications(encounterId)
      encounterMessages.value = response?.data ?? []
    } catch (error) {
      console.error(error)
    }
  }

  async function getEncounterOrders(encounterId: string) {
    try {
      // Fetch profile
      const response = await api.encounter.encounterGetOrders(encounterId)
      encounterOrders.value = response?.data ?? []
    } catch (error) {
      console.error(error)
    }
  }

  async function getPatientProfile(patientId: string) {
    try {
      // Fetch profile
      const response = await api.patients.patientGetPatientProfile({ patientId })
      patientProfile.value = response?.data ?? []
    } catch (error) {
      console.error(error)
    }
  }

  async function getPatientImmunization(patientId: string) {
    try {
      // Fetch profile
      const response = await api.patients.patientListImmunizations({ patientId })
      patientImmunizations.value = response?.data ?? []
    } catch (error) {
      console.error(error)
    }
  }

  async function getPatientActiveMedications(patientId: string) {
    try {
      // Fetch profile
      const response = await api.patients.patientListPatientActiveMedicationOrders(patientId)
      patientActiveMedicationOrders.value = response?.data ?? []
    } catch (error) {
      console.error(error)
    }
  }

  async function getPatientProblems(patientId: string) {
    try {
      // Fetch profile
      const response = await api.patients.patientListPatientProblems(patientId)
      patientProblems.value = response?.data ?? []
    } catch (error) {
      console.error(error)
    }
  }

  async function getCare(patientId: string, encounterId: string) {
    try {
      // Fetch profile
      const response = await api.patients.patientListPatientCare(patientId, { encounterId })
      patientCareTeams.value = response?.data?.careTeamItems ?? []
      patientCarePlans.value = response?.data?.carePlanItems ?? []
      patientCareEvents.value = response.data?.careEvents ?? []
    } catch (error) {
      console.error(error)
    }
  }

  function setEncounterBoxes() {
    const storedEncounterBoxes = localStorage.getItem('encounterBoxes')
    if (storedEncounterBoxes) {
      encounterBoxes.value = JSON.parse(storedEncounterBoxes)
    } else {
      localStorage.removeItem('encounterBoxes')
      parentContainerHeight.value = window.innerHeight - 200
      parentContainerWidth.value = window.innerWidth
      encounterBoxes.value = [
        {
          type: 'active-patients',
          isOpen: !isTablet.value && !isMobile.value,
          height: parentContainerHeight.value,
          width: 250,
          x: 0,
          y: 0,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'summary',
          isOpen: !isTablet.value && !isMobile.value,
          height: parentContainerHeight.value,
          width: 300,
          x: parentContainerWidth.value - 300,
          y: 0,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'vitals',
          isOpen: false,
          height: isMobile.value ? parentContainerHeight.value : 400,
          width: isMobile.value ? parentContainerWidth.value : 700,
          x: isMobile.value ? 0 : undefined,
          y: isMobile.value ? 0 : undefined,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'scratch',
          isOpen: false,
          height: isMobile.value ? parentContainerHeight.value : 400,
          width: isMobile.value ? parentContainerWidth.value : 700,
          x: isMobile.value ? 0 : undefined,
          y: isMobile.value ? 0 : undefined,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'care',
          isOpen: false,
          height: isMobile.value ? parentContainerHeight.value : 400,
          width: isMobile.value ? parentContainerWidth.value : 700,
          x: isMobile.value ? 0 : undefined,
          y: isMobile.value ? 0 : undefined,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'questionnaires',
          isOpen: false,
          height: isMobile.value ? parentContainerHeight.value : 400,
          width: isMobile.value ? parentContainerWidth.value : 700,
          x: isMobile.value ? 0 : undefined,
          y: isMobile.value ? 0 : undefined,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'labs',
          isOpen: false,
          height: 400,
          width: 700,
          x: undefined,
          y: undefined,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'imaging',
          isOpen: false,
          height: 400,
          width: 700,
          x: undefined,
          y: undefined,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'comms',
          isOpen: false,
          height: 400,
          width: 700,
          x: undefined,
          y: undefined,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'orders',
          isOpen: false,
          height: 400,
          width: 700,
          x: undefined,
          y: undefined,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'notes',
          isOpen: false,
          height: 400,
          width: 700,
          x: undefined,
          y: undefined,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'info',
          isOpen: false,
          height: isMobile.value ? parentContainerHeight.value : 400,
          width: isMobile.value ? parentContainerWidth.value : 700,
          x: isMobile.value ? 0 : undefined,
          y: isMobile.value ? 0 : undefined,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
        {
          type: 'info2',
          isOpen: false,
          height: isMobile.value ? parentContainerHeight.value : 400,
          width: isMobile.value ? parentContainerWidth.value : 700,
          x: isMobile.value ? 0 : undefined,
          y: isMobile.value ? 0 : undefined,
          z: 1,
          isExpanded: false,
          oldX: undefined,
          oldY: undefined,
          key: generateGuid(),
        },
      ]
      localStorage.setItem('encounterBoxes', JSON.stringify(encounterBoxes.value))
      localStorage.setItem('parentContainerHeight', JSON.stringify(parentContainerHeight.value))
      localStorage.setItem('parentContainerWidth', JSON.stringify(parentContainerWidth.value))
    }
  }

  function updateEncounterBoxes(encounterBox: EncounterBox) {
    encounterBoxes.value = encounterBoxes.value.map((eb) =>
      eb.type === encounterBox.type ? encounterBox : eb,
    )
    localStorage.removeItem('encounterBoxes')
    localStorage.setItem('encounterBoxes', JSON.stringify(encounterBoxes.value))
  }

  function toggleIsBoxOpen(type: string) {
    const box = encounterBoxes.value.filter((x) => x.type == type)[0]
    if (box) {
      box.isOpen = !box.isOpen
      updateEncounterBoxes(box)
    }
  }

  function setActiveBox(type: string) {
    encounterBoxes.value.forEach((box) => {
      box.isOpen = box.type === type ? true : box.isOpen
      box.z = box.type === type ? 10 : 1
    })
    localStorage.removeItem('encounterBoxes')
    localStorage.setItem('encounterBoxes', JSON.stringify(encounterBoxes.value))
  }

  function reset() {
    localStorage.removeItem('encounterBoxes')
    window.location.reload()
  }

  function updateBoxDimensions(containerWidth: number, containerHeight: number) {
    encounterBoxes.value.forEach((box) => {
      const relWidth = box.width / parentContainerWidth.value
      box.width = relWidth * containerWidth
      const relX = box.x! / parentContainerWidth.value
      box.x = relX * containerWidth

      const relHeight = box.height / parentContainerHeight.value
      const relY = box.y! / parentContainerHeight.value
      box.height = relHeight * (containerHeight - 200)
      box.y = relY * (containerHeight - 200)

      box.key = generateGuid()
    })
    parentContainerWidth.value = containerWidth
    parentContainerHeight.value = containerHeight - 200
    localStorage.removeItem('encounterBoxes')
    localStorage.setItem('encounterBoxes', JSON.stringify(encounterBoxes.value))
    localStorage.setItem('parentContainerHeight', JSON.stringify(parentContainerHeight.value))
    localStorage.setItem('parentContainerWidth', JSON.stringify(parentContainerWidth.value))
  }

  return {
    activeEncounters,
    getLatestPatientEncounter,
    selectedEncounter,
    laboratoryResults,
    imagingResults,
    encounterMessages,
    encounterOrders,
    encounterBoxes,
    parentContainerWidth,
    parentContainerHeight,
    vitalSigns,
    patientProfile,
    patientImmunizations,
    patientActiveMedicationOrders,
    patientProblems,
    patientCareTeams,
    patientCarePlans,
    patientCareEvents,
    getCare,
    getPatientVitalSigns,
    questionnaires,
    isMobile,
    isTablet,
    editingOrder,
    editingBundleId,
    setEditingOrder,
    setSelectedEncounter,
    getEncounter,
    getActiveEncounters,
    setSelectedEncounterById,
    setSelectedEncounterDefault,
    getEncounterMessages,
    getEncounterOrders,
    getPatientActiveMedications,
    getPatientProblems,
    setEncounterBoxes,
    updateEncounterBoxes,
    toggleIsBoxOpen,
    setActiveBox,
    reset,
    updateBoxDimensions,
  }
})
